import '../constants/app_strings.dart';
import '../config/app_config.dart';

class Validators {
  // Email validation
  static String? validateEmail(String? value) {
    if (value == null || value.isEmpty) {
      return AppStrings.emailRequired;
    }
    
    if (value.length > AppConfig.maxEmailLength) {
      return 'البريد الإلكتروني طويل جداً';
    }
    
    final emailRegex = RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$');
    if (!emailRegex.hasMatch(value)) {
      return AppStrings.emailInvalid;
    }
    
    return null;
  }

  // Password validation
  static String? validatePassword(String? value) {
    if (value == null || value.isEmpty) {
      return AppStrings.passwordRequired;
    }
    
    if (value.length < AppConfig.minPasswordLength) {
      return AppStrings.passwordTooShort;
    }
    
    if (value.length > AppConfig.maxPasswordLength) {
      return 'كلمة المرور طويلة جداً';
    }
    
    return null;
  }

  // Name validation
  static String? validateName(String? value) {
    if (value == null || value.isEmpty) {
      return 'يرجى إدخال الاسم';
    }
    
    if (value.length < 2) {
      return 'الاسم قصير جداً';
    }
    
    if (value.length > 50) {
      return 'الاسم طويل جداً';
    }
    
    return null;
  }

  // Phone validation
  static String? validatePhone(String? value) {
    if (value == null || value.isEmpty) {
      return 'يرجى إدخال رقم الهاتف';
    }
    
    // Remove spaces and special characters
    final cleanPhone = value.replaceAll(RegExp(r'[^\d+]'), '');
    
    // Yemen phone number validation
    final yemenPhoneRegex = RegExp(r'^(\+967|967|0)?[1-9]\d{7,8}$');
    if (!yemenPhoneRegex.hasMatch(cleanPhone)) {
      return 'رقم الهاتف غير صحيح';
    }
    
    return null;
  }

  // Price validation
  static String? validatePrice(String? value) {
    if (value == null || value.isEmpty) {
      return 'يرجى إدخال السعر';
    }
    
    final price = double.tryParse(value);
    if (price == null) {
      return 'السعر غير صحيح';
    }
    
    if (price <= 0) {
      return 'السعر يجب أن يكون أكبر من صفر';
    }
    
    if (price > 999999999) {
      return 'السعر كبير جداً';
    }
    
    return null;
  }

  // Area validation
  static String? validateArea(String? value) {
    if (value == null || value.isEmpty) {
      return 'يرجى إدخال المساحة';
    }
    
    final area = double.tryParse(value);
    if (area == null) {
      return 'المساحة غير صحيحة';
    }
    
    if (area <= 0) {
      return 'المساحة يجب أن تكون أكبر من صفر';
    }
    
    if (area > 100000) {
      return 'المساحة كبيرة جداً';
    }
    
    return null;
  }

  // Required field validation
  static String? validateRequired(String? value, String fieldName) {
    if (value == null || value.isEmpty) {
      return 'يرجى إدخال $fieldName';
    }
    return null;
  }

  // Number validation
  static String? validateNumber(String? value, {int? min, int? max}) {
    if (value == null || value.isEmpty) {
      return 'يرجى إدخال رقم صحيح';
    }
    
    final number = int.tryParse(value);
    if (number == null) {
      return 'الرقم غير صحيح';
    }
    
    if (min != null && number < min) {
      return 'الرقم يجب أن يكون $min على الأقل';
    }
    
    if (max != null && number > max) {
      return 'الرقم يجب أن يكون $max على الأكثر';
    }
    
    return null;
  }

  // URL validation
  static String? validateUrl(String? value) {
    if (value == null || value.isEmpty) {
      return null; // URL is optional
    }
    
    final urlRegex = RegExp(
      r'^https?:\/\/(www\.)?[-a-zA-Z0-9@:%._\+~#=]{1,256}\.[a-zA-Z0-9()]{1,6}\b([-a-zA-Z0-9()@:%_\+.~#?&//=]*)$'
    );
    
    if (!urlRegex.hasMatch(value)) {
      return 'الرابط غير صحيح';
    }
    
    return null;
  }
}
