// This is a basic Flutter widget test.
//
// To perform an interaction with a widget in your test, use the WidgetTester
// utility in the flutter_test package. For example, you can send tap and scroll
// gestures. You can also use WidgetTester to find child widgets in the widget
// tree, read text, and verify that the values of widget properties are correct.

import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:real_estate_app/main.dart';

void main() {
  testWidgets('App should start and show splash screen', (WidgetTester tester) async {
    // Build our app and trigger a frame.
    await tester.pumpWidget(const ProviderScope(child: RealEstateApp()));

    // Verify that splash screen is shown
    expect(find.text('دليلك السكني'), findsOneWidget);

    // Wait for splash screen animation
    await tester.pump(const Duration(seconds: 2));

    // Should navigate to onboarding or main page
    await tester.pumpAndSettle();
  });

  testWidgets('Theme switching should work', (WidgetTester tester) async {
    await tester.pumpWidget(const ProviderScope(child: RealEstateApp()));
    await tester.pumpAndSettle();

    // App should start without errors
    expect(tester.takeException(), isNull);
  });
}
