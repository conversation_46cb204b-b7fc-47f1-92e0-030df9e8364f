import 'package:flutter/material.dart';

class AppIcons {
  // Navigation Icons
  static const IconData home = Icons.home_outlined;
  static const IconData homeFilled = Icons.home;
  static const IconData search = Icons.search;
  static const IconData map = Icons.map_outlined;
  static const IconData mapFilled = Icons.map;
  static const IconData add = Icons.add_circle_outline;
  static const IconData addFilled = Icons.add_circle;
  static const IconData favorite = Icons.favorite_outline;
  static const IconData favoriteFilled = Icons.favorite;
  static const IconData profile = Icons.person_outline;
  static const IconData profileFilled = Icons.person;
  
  // Property Icons
  static const IconData property = Icons.home_work;
  static const IconData apartment = Icons.apartment;
  static const IconData villa = Icons.house;
  static const IconData office = Icons.business;
  static const IconData land = Icons.landscape;
  static const IconData warehouse = Icons.warehouse;
  
  // Feature Icons
  static const IconData bed = Icons.bed;
  static const IconData bathroom = Icons.bathtub;
  static const IconData area = Icons.square_foot;
  static const IconData parking = Icons.local_parking;
  static const IconData elevator = Icons.elevator;
  static const IconData garden = Icons.grass;
  static const IconData pool = Icons.pool;
  static const IconData gym = Icons.fitness_center;
  static const IconData security = Icons.security;
  static const IconData wifi = Icons.wifi;
  
  // Action Icons
  static const IconData call = Icons.call;
  static const IconData message = Icons.message;
  static const IconData share = Icons.share;
  static const IconData location = Icons.location_on;
  static const IconData camera = Icons.camera_alt;
  static const IconData gallery = Icons.photo_library;
  static const IconData edit = Icons.edit;
  static const IconData delete = Icons.delete;
  static const IconData filter = Icons.filter_list;
  static const IconData sort = Icons.sort;
  
  // Status Icons
  static const IconData verified = Icons.verified;
  static const IconData star = Icons.star;
  static const IconData starOutline = Icons.star_outline;
  static const IconData check = Icons.check_circle;
  static const IconData warning = Icons.warning;
  static const IconData error = Icons.error;
  static const IconData info = Icons.info;
  
  // UI Icons
  static const IconData back = Icons.arrow_back;
  static const IconData forward = Icons.arrow_forward;
  static const IconData up = Icons.keyboard_arrow_up;
  static const IconData down = Icons.keyboard_arrow_down;
  static const IconData close = Icons.close;
  static const IconData menu = Icons.menu;
  static const IconData more = Icons.more_vert;
  static const IconData settings = Icons.settings;
  static const IconData notification = Icons.notifications;
  static const IconData notificationOff = Icons.notifications_off;
  
  // Auth Icons
  static const IconData email = Icons.email;
  static const IconData password = Icons.lock;
  static const IconData visibility = Icons.visibility;
  static const IconData visibilityOff = Icons.visibility_off;
  static const IconData login = Icons.login;
  static const IconData logout = Icons.logout;
  
  // Theme Icons
  static const IconData lightMode = Icons.light_mode;
  static const IconData darkMode = Icons.dark_mode;
  static const IconData autoMode = Icons.brightness_auto;
  
  // Language Icons
  static const IconData language = Icons.language;
  static const IconData translate = Icons.translate;
}
