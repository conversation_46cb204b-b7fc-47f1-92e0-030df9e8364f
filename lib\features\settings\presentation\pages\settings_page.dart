import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../../../core/constants/app_constants.dart';
import '../../../../core/providers/theme_provider.dart';

class SettingsPage extends ConsumerWidget {
  const SettingsPage({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final isDarkMode = ref.watch(themeProvider);

    return Scaffold(
      appBar: AppBar(
        title: const Text('الإعدادات'),
        centerTitle: true,
      ),
      body: ListView(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        children: [
          // Appearance Section
          _buildSectionHeader('المظهر'),
          Card(
            child: Column(
              children: [
                SwitchListTile(
                  title: const Text('الوضع الليلي'),
                  subtitle: const Text('تفعيل الثيم الداكن'),
                  value: isDarkMode,
                  onChanged: (value) {
                    ref.read(themeProvider.notifier).toggleTheme();
                  },
                  secondary: Icon(
                    isDarkMode ? Icons.dark_mode : Icons.light_mode,
                  ),
                ),
                ListTile(
                  leading: const Icon(Icons.language),
                  title: const Text('اللغة'),
                  subtitle: const Text('العربية'),
                  trailing: const Icon(Icons.arrow_forward_ios),
                  onTap: () {
                    _showLanguageDialog(context);
                  },
                ),
                ListTile(
                  leading: const Icon(Icons.text_fields),
                  title: const Text('حجم الخط'),
                  subtitle: const Text('متوسط'),
                  trailing: const Icon(Icons.arrow_forward_ios),
                  onTap: () {
                    _showFontSizeDialog(context);
                  },
                ),
              ],
            ),
          ),

          const SizedBox(height: 24),

          // Notifications Section
          _buildSectionHeader('الإشعارات'),
          Card(
            child: Column(
              children: [
                SwitchListTile(
                  title: const Text('إشعارات الرسائل'),
                  subtitle: const Text('تلقي إشعارات الرسائل الجديدة'),
                  value: true,
                  onChanged: (value) {
                    // TODO: Implement notification settings
                  },
                  secondary: const Icon(Icons.message),
                ),
                SwitchListTile(
                  title: const Text('إشعارات العقارات'),
                  subtitle: const Text('تحديثات العقارات المفضلة'),
                  value: true,
                  onChanged: (value) {
                    // TODO: Implement notification settings
                  },
                  secondary: const Icon(Icons.home),
                ),
                SwitchListTile(
                  title: const Text('إشعارات النظام'),
                  subtitle: const Text('تحديثات التطبيق والنظام'),
                  value: true,
                  onChanged: (value) {
                    // TODO: Implement notification settings
                  },
                  secondary: const Icon(Icons.system_update),
                ),
              ],
            ),
          ),

          const SizedBox(height: 24),

          // Privacy Section
          _buildSectionHeader('الخصوصية والأمان'),
          Card(
            child: Column(
              children: [
                ListTile(
                  leading: const Icon(Icons.lock),
                  title: const Text('تغيير كلمة المرور'),
                  trailing: const Icon(Icons.arrow_forward_ios),
                  onTap: () {
                    _showChangePasswordDialog(context);
                  },
                ),
                ListTile(
                  leading: const Icon(Icons.privacy_tip),
                  title: const Text('سياسة الخصوصية'),
                  trailing: const Icon(Icons.arrow_forward_ios),
                  onTap: () {
                    _showPrivacyPolicy(context);
                  },
                ),
                ListTile(
                  leading: const Icon(Icons.description),
                  title: const Text('شروط الاستخدام'),
                  trailing: const Icon(Icons.arrow_forward_ios),
                  onTap: () {
                    _showTermsOfService(context);
                  },
                ),
              ],
            ),
          ),

          const SizedBox(height: 24),

          // App Section
          _buildSectionHeader('التطبيق'),
          Card(
            child: Column(
              children: [
                ListTile(
                  leading: const Icon(Icons.storage),
                  title: const Text('إدارة التخزين'),
                  subtitle: const Text('مسح البيانات المؤقتة'),
                  trailing: const Icon(Icons.arrow_forward_ios),
                  onTap: () {
                    _showStorageDialog(context);
                  },
                ),
                ListTile(
                  leading: const Icon(Icons.backup),
                  title: const Text('النسخ الاحتياطي'),
                  subtitle: const Text('نسخ احتياطي للبيانات'),
                  trailing: const Icon(Icons.arrow_forward_ios),
                  onTap: () {
                    _showBackupDialog(context);
                  },
                ),
                ListTile(
                  leading: const Icon(Icons.info),
                  title: const Text('معلومات التطبيق'),
                  subtitle: const Text('الإصدار 1.0.0'),
                  trailing: const Icon(Icons.arrow_forward_ios),
                  onTap: () {
                    _showAppInfo(context);
                  },
                ),
              ],
            ),
          ),

          const SizedBox(height: 24),

          // Danger Zone
          _buildSectionHeader('منطقة الخطر'),
          Card(
            color: Colors.red[50],
            child: Column(
              children: [
                ListTile(
                  leading: const Icon(Icons.delete_forever, color: Colors.red),
                  title: const Text(
                    'حذف الحساب',
                    style: TextStyle(color: Colors.red),
                  ),
                  subtitle: const Text('حذف الحساب نهائياً'),
                  trailing: const Icon(Icons.arrow_forward_ios, color: Colors.red),
                  onTap: () {
                    _showDeleteAccountDialog(context);
                  },
                ),
              ],
            ),
          ),

          const SizedBox(height: 40),
        ],
      ),
    );
  }

  Widget _buildSectionHeader(String title) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8, top: 8),
      child: Text(
        title,
        style: const TextStyle(
          fontSize: 16,
          fontWeight: FontWeight.bold,
          color: Colors.grey,
        ),
      ),
    );
  }

  void _showLanguageDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('اختيار اللغة'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            RadioListTile<String>(
              title: const Text('العربية'),
              value: 'ar',
              groupValue: 'ar',
              onChanged: (value) {
                Navigator.pop(context);
              },
            ),
            RadioListTile<String>(
              title: const Text('English'),
              value: 'en',
              groupValue: 'ar',
              onChanged: (value) {
                Navigator.pop(context);
                // TODO: Implement language change
              },
            ),
          ],
        ),
      ),
    );
  }

  void _showFontSizeDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('حجم الخط'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            RadioListTile<String>(
              title: const Text('صغير'),
              value: 'small',
              groupValue: 'medium',
              onChanged: (value) => Navigator.pop(context),
            ),
            RadioListTile<String>(
              title: const Text('متوسط'),
              value: 'medium',
              groupValue: 'medium',
              onChanged: (value) => Navigator.pop(context),
            ),
            RadioListTile<String>(
              title: const Text('كبير'),
              value: 'large',
              groupValue: 'medium',
              onChanged: (value) => Navigator.pop(context),
            ),
          ],
        ),
      ),
    );
  }

  void _showChangePasswordDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تغيير كلمة المرور'),
        content: const Text('سيتم إضافة هذه الميزة قريباً'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إغلاق'),
          ),
        ],
      ),
    );
  }

  void _showPrivacyPolicy(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('سياسة الخصوصية'),
        content: const SingleChildScrollView(
          child: Text(
            'نحن نحترم خصوصيتك ونلتزم بحماية بياناتك الشخصية.\n\n'
            '• لا نشارك بياناتك مع أطراف ثالثة\n'
            '• نستخدم بياناتك فقط لتحسين الخدمة\n'
            '• يمكنك حذف حسابك في أي وقت\n'
            '• نحمي بياناتك بأحدث تقنيات الأمان',
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إغلاق'),
          ),
        ],
      ),
    );
  }

  void _showTermsOfService(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('شروط الاستخدام'),
        content: const SingleChildScrollView(
          child: Text(
            'باستخدام هذا التطبيق، فإنك توافق على:\n\n'
            '• استخدام التطبيق للأغراض القانونية فقط\n'
            '• عدم نشر محتوى مخالف أو مضلل\n'
            '• احترام حقوق المستخدمين الآخرين\n'
            '• تقديم معلومات صحيحة عن العقارات',
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إغلاق'),
          ),
        ],
      ),
    );
  }

  void _showStorageDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('إدارة التخزين'),
        content: const Text('سيتم إضافة هذه الميزة قريباً'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إغلاق'),
          ),
        ],
      ),
    );
  }

  void _showBackupDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('النسخ الاحتياطي'),
        content: const Text('سيتم إضافة هذه الميزة قريباً'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إغلاق'),
          ),
        ],
      ),
    );
  }

  void _showAppInfo(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('معلومات التطبيق'),
        content: const Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('اسم التطبيق: دليلك السكني'),
            SizedBox(height: 8),
            Text('الإصدار: 1.0.0'),
            SizedBox(height: 8),
            Text('المطور: فريق دليلك السكني'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إغلاق'),
          ),
        ],
      ),
    );
  }

  void _showDeleteAccountDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('حذف الحساب'),
        content: const Text(
          'تحذير: هذا الإجراء لا يمكن التراجع عنه.\n\n'
          'سيتم حذف جميع بياناتك نهائياً.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              // TODO: Implement account deletion
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
            child: const Text('حذف الحساب'),
          ),
        ],
      ),
    );
  }
}
