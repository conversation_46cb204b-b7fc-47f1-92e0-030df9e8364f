import 'dart:ui';

class FontConfig {
  // Font preferences
  static const String primaryArabicFont = 'Cairo';
  static const String secondaryArabicFont = 'Amiri';
  static const String fallbackFont = 'Arial';

  // Font loading settings
  static const bool enableGoogleFonts = true;
  static const bool enableFontCaching = true;
  static const Duration fontLoadTimeout = Duration(seconds: 5);

  // Font sizes
  static const double displayLargeSize = 32.0;
  static const double displayMediumSize = 28.0;
  static const double headlineLargeSize = 24.0;
  static const double headlineMediumSize = 20.0;
  static const double titleLargeSize = 18.0;
  static const double titleMediumSize = 16.0;
  static const double bodyLargeSize = 16.0;
  static const double bodyMediumSize = 14.0;
  static const double labelLargeSize = 14.0;
  static const double captionSize = 12.0;

  // Font weights
  static const FontWeight lightWeight = FontWeight.w300;
  static const FontWeight regularWeight = FontWeight.w400;
  static const FontWeight mediumWeight = FontWeight.w500;
  static const FontWeight semiBoldWeight = FontWeight.w600;
  static const FontWeight boldWeight = FontWeight.w700;

  // Line heights
  static const double defaultLineHeight = 1.4;
  static const double headingLineHeight = 1.2;
  static const double bodyLineHeight = 1.5;

  // Letter spacing
  static const double defaultLetterSpacing = 0.0;
  static const double headingLetterSpacing = -0.5;
  static const double buttonLetterSpacing = 0.5;

  // Available Arabic fonts for Google Fonts
  static const List<String> availableArabicFonts = [
    'Cairo',
    'Amiri',
    'Noto Sans Arabic',
    'Tajawal',
    'Almarai',
    'Changa',
    'El Messiri',
    'Harmattan',
    'Lateef',
    'Markazi Text',
    'Reem Kufi',
    'Scheherazade New',
  ];

  // Font feature settings for Arabic
  static const List<String> arabicFontFeatures = [
    'kern', // Kerning
    'liga', // Ligatures
    'calt', // Contextual alternates
    'mark', // Mark positioning
    'mkmk', // Mark to mark positioning
  ];

  // RTL text direction settings
  static const bool enableRTL = true;
  static const bool autoDetectTextDirection = true;

  // Font loading strategies
  static const String immediate = 'immediate';    // Load fonts immediately
  static const String lazy = 'lazy';              // Load fonts when needed
  static const String preload = 'preload';        // Preload common fonts
  static const String fallback = 'fallback';      // Use fallback fonts only

  static const String defaultStrategy = lazy;

  // Error handling
  static const bool silentFontErrors = true;
  static const bool logFontErrors = true;
  static const int maxRetryAttempts = 3;

  // Performance settings
  static const int maxCachedFonts = 50;
  static const Duration cacheExpiration = Duration(hours: 24);
  static const bool enableFontPreloading = false;

  // Accessibility settings
  static const double minFontSize = 12.0;
  static const double maxFontSize = 48.0;
  static const double fontScaleFactor = 1.0;

  // Platform-specific settings
  static const Map<String, String> platformFallbacks = {
    'android': 'Roboto',
    'ios': 'SF Pro Text',
    'web': 'Arial',
    'windows': 'Segoe UI',
    'macos': 'SF Pro Text',
    'linux': 'Ubuntu',
  };
}
