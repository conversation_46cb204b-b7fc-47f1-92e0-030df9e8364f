# untitled4

A new Flutter project.

## Getting Started

# دليلك السكني - Real Estate App

تطبيق عقاري عربي متقدم مبني بـ Flutter يوفر تجربة مستخدم متميزة للبحث عن العقارات.

## الميزات المنجزة ✅

### المرحلة الأولى - الواجهات الأساسية
- ✅ **Splash Screen** - شاشة ترحيب أنيقة مع شعار التطبيق
- ✅ **Onboarding** - 3 شاشات تعريفية تفاعلية مع مؤشرات التقدم
- ✅ **Login Screen** - واجهة تسجيل دخول متكاملة مع التحقق من البيانات
- ✅ **نظام التنقل** - GoRouter للتنقل السلس بين الشاشات
- ✅ **نظام التصميم** - ثيم فاتح وداكن مع دعم RTL
- ✅ **الخطوط العربية** - خط Cairo من Google Fonts

### المرحلة الثانية - البنية المتقدمة
- ✅ **نماذج البيانات** - UserModel, PropertyModel مع LocationModel
- ✅ **إدارة الحالة** - Riverpod مع AuthProvider متكامل
- ✅ **صفحة التسجيل** - نموذج إنشاء حساب جديد مع التحقق
- ✅ **الشاشة الرئيسية** - شريط تنقل سفلي مع 5 أقسام
- ✅ **صفحة الاستكشاف** - قائمة العقارات مع البحث والفلاتر
- ✅ **صفحة الملف الشخصي** - عرض بيانات المستخدم وتسجيل الخروج
- ✅ **نظام التحقق** - Validators للبريد والكلمة والهاتف
- ✅ **نظام التنسيق** - Formatters للأسعار والتواريخ والأرقام

## التقنيات المستخدمة 🛠️

- **Flutter** - إطار العمل الأساسي
- **Dart** - لغة البرمجة
- **GoRouter** - للتنقل والتوجيه
- **Riverpod** - لإدارة الحالة
- **Google Fonts** - للخطوط العربية
- **Smooth Page Indicator** - لمؤشرات الصفحات
- **Lottie** - للرسوم المتحركة (جاهز للاستخدام)

## هيكل المشروع 📁

```
lib/
├── core/
│   ├── constants/
│   │   └── app_constants.dart      # الثوابت العامة
│   ├── theme/
│   │   └── app_theme.dart          # نظام التصميم والألوان
│   └── router/
│       └── app_router.dart         # نظام التوجيه
├── features/
│   ├── splash/
│   │   └── presentation/
│   │       └── pages/
│   │           └── splash_page.dart
│   ├── onboarding/
│   │   └── presentation/
│   │       ├── pages/
│   │       │   └── onboarding_page.dart
│   │       └── widgets/
│   │           └── onboarding_content.dart
│   ├── auth/
│   │   └── presentation/
│   │       └── pages/
│   │           └── login_page.dart
│   └── home/
│       └── presentation/
│           └── pages/
│               └── home_page.dart
└── main.dart                       # نقطة البداية
```

## كيفية التشغيل 🚀

1. **تأكد من تثبيت Flutter:**
   ```bash
   flutter doctor
   ```

2. **تحميل التبعيات:**
   ```bash
   flutter pub get
   ```

3. **تشغيل التطبيق:**
   ```bash
   flutter run
   ```

## الميزات القادمة 🔮

### المرحلة الثانية - البنية الأساسية
- [ ] نظام المصادقة الكامل
- [ ] قاعدة البيانات المحلية
- [ ] إدارة الحالة المتقدمة
- [ ] نظام الإشعارات

### المرحلة الثالثة - الميزات المتقدمة
- [ ] البحث والفلاتر
- [ ] الخرائط والموقع
- [ ] المفضلة والمقارنة
- [ ] نظام الرسائل
- [ ] لوحة تحكم المشرف

### المرحلة الرابعة - التحسينات
- [ ] الأداء والتحسين
- [ ] الأوفلاين والمزامنة
- [ ] التحليلات والإحصائيات
- [ ] الاختبارات الشاملة

## المساهمة 🤝

هذا المشروع في مرحلة التطوير النشط. للمساهمة:

1. Fork المشروع
2. إنشاء branch جديد للميزة
3. Commit التغييرات
4. Push إلى Branch
5. إنشاء Pull Request

## الترخيص 📄

هذا المشروع مرخص تحت رخصة MIT - راجع ملف [LICENSE](LICENSE) للتفاصيل.

---

**تم تطوير المرحلة الأولى بنجاح ✨**

التطبيق جاهز الآن للاختبار والانتقال للمرحلة التالية من التطوير.
