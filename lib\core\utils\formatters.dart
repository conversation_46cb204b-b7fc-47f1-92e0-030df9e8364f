import 'package:intl/intl.dart';

import '../constants/app_strings.dart';

class Formatters {
  // Number formatters
  static final _numberFormat = NumberFormat('#,##0', 'ar');
  static final _decimalFormat = NumberFormat('#,##0.00', 'ar');
  static final _currencyFormat = NumberFormat('#,##0', 'ar');

  // Format number with Arabic locale
  static String formatNumber(num number) {
    return _numberFormat.format(number);
  }

  // Format decimal number
  static String formatDecimal(double number) {
    return _decimalFormat.format(number);
  }

  // Format price with currency
  static String formatPrice(num price, {String currency = 'YER'}) {
    final formattedPrice = _currencyFormat.format(price);
    switch (currency.toUpperCase()) {
      case 'YER':
        return '$formattedPrice ${AppStrings.yer}';
      case 'USD':
        return '\$$formattedPrice';
      default:
        return '$formattedPrice $currency';
    }
  }

  // Format area
  static String formatArea(num area) {
    return '${formatNumber(area)} ${AppStrings.sqm}';
  }

  // Format phone number
  static String formatPhoneNumber(String phone) {
    // Remove all non-digit characters except +
    String cleaned = phone.replaceAll(RegExp(r'[^\d+]'), '');

    // Add Yemen country code if not present
    if (!cleaned.startsWith('+967') && !cleaned.startsWith('967')) {
      if (cleaned.startsWith('0')) {
        cleaned = '+967${cleaned.substring(1)}';
      } else {
        cleaned = '+967$cleaned';
      }
    } else if (cleaned.startsWith('967')) {
      cleaned = '+$cleaned';
    }

    // Format: +967 X XXX XXXX
    if (cleaned.length >= 12) {
      return '${cleaned.substring(0, 4)} ${cleaned.substring(4, 5)} ${cleaned.substring(5, 8)} ${cleaned.substring(8)}';
    }

    return cleaned;
  }

  // Format date
  static String formatDate(DateTime date, {String? locale}) {
    final formatter = DateFormat('dd/MM/yyyy', locale ?? 'ar');
    return formatter.format(date);
  }

  // Format date with time
  static String formatDateTime(DateTime dateTime, {String? locale}) {
    final formatter = DateFormat('dd/MM/yyyy HH:mm', locale ?? 'ar');
    return formatter.format(dateTime);
  }

  // Format relative time (منذ ساعة، منذ يوم، إلخ)
  static String formatRelativeTime(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);

    if (difference.inDays > 365) {
      final years = (difference.inDays / 365).floor();
      return years == 1 ? 'منذ سنة' : 'منذ $years سنوات';
    } else if (difference.inDays > 30) {
      final months = (difference.inDays / 30).floor();
      return months == 1 ? 'منذ شهر' : 'منذ $months أشهر';
    } else if (difference.inDays > 0) {
      return difference.inDays == 1 ? 'منذ يوم' : 'منذ ${difference.inDays} أيام';
    } else if (difference.inHours > 0) {
      return difference.inHours == 1 ? 'منذ ساعة' : 'منذ ${difference.inHours} ساعات';
    } else if (difference.inMinutes > 0) {
      return difference.inMinutes == 1 ? 'منذ دقيقة' : 'منذ ${difference.inMinutes} دقائق';
    } else {
      return 'الآن';
    }
  }

  // Format file size
  static String formatFileSize(int bytes) {
    if (bytes < 1024) {
      return '$bytes بايت';
    } else if (bytes < 1024 * 1024) {
      return '${(bytes / 1024).toStringAsFixed(1)} كيلوبايت';
    } else if (bytes < 1024 * 1024 * 1024) {
      return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} ميجابايت';
    } else {
      return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(1)} جيجابايت';
    }
  }

  // Format percentage
  static String formatPercentage(double value) {
    return '${(value * 100).toStringAsFixed(1)}%';
  }

  // Format duration
  static String formatDuration(Duration duration) {
    if (duration.inDays > 0) {
      return '${duration.inDays} يوم';
    } else if (duration.inHours > 0) {
      return '${duration.inHours} ساعة';
    } else if (duration.inMinutes > 0) {
      return '${duration.inMinutes} دقيقة';
    } else {
      return '${duration.inSeconds} ثانية';
    }
  }

  // Capitalize first letter
  static String capitalize(String text) {
    if (text.isEmpty) return text;
    return text[0].toUpperCase() + text.substring(1);
  }

  // Truncate text with ellipsis
  static String truncate(String text, int maxLength) {
    if (text.length <= maxLength) return text;
    return '${text.substring(0, maxLength)}...';
  }

  // Format property type
  static String formatPropertyType(String type) {
    switch (type.toLowerCase()) {
      case 'apartment':
        return 'شقة';
      case 'villa':
        return 'فيلا';
      case 'house':
        return 'منزل';
      case 'office':
        return 'مكتب';
      case 'shop':
        return 'محل';
      case 'warehouse':
        return 'مستودع';
      case 'land':
        return 'أرض';
      default:
        return type;
    }
  }

  // Format property status
  static String formatPropertyStatus(String status) {
    switch (status.toLowerCase()) {
      case 'for_sale':
        return AppStrings.forSale;
      case 'for_rent':
        return AppStrings.forRent;
      case 'sold':
        return AppStrings.sold;
      case 'rented':
        return AppStrings.rented;
      default:
        return status;
    }
  }

  /// Format time only
  static String formatTime(DateTime dateTime) {
    return DateFormat('HH:mm', 'ar').format(dateTime);
  }

  /// Format message date
  static String formatMessageDate(DateTime dateTime) {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final messageDate = DateTime(dateTime.year, dateTime.month, dateTime.day);

    if (messageDate == today) {
      return 'اليوم';
    } else if (messageDate == today.subtract(const Duration(days: 1))) {
      return 'أمس';
    } else if (now.difference(messageDate).inDays < 7) {
      return DateFormat('EEEE', 'ar').format(dateTime);
    } else {
      return DateFormat('dd/MM/yyyy', 'ar').format(dateTime);
    }
  }
}
