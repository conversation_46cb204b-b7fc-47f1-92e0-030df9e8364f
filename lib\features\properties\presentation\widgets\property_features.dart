import 'package:flutter/material.dart';

import '../../../../core/constants/app_constants.dart';

class PropertyFeatures extends StatelessWidget {
  final List<String> features;

  const PropertyFeatures({
    super.key,
    required this.features,
  });

  @override
  Widget build(BuildContext context) {
    if (features.isEmpty) return const SizedBox.shrink();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'المميزات',
          style: Theme.of(context).textTheme.titleLarge,
        ),
        const SizedBox(height: 12),
        
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children: features.map((feature) {
            return Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
              decoration: BoxDecoration(
                color: Theme.of(context).primaryColor.withOpacity(0.1),
                borderRadius: BorderRadius.circular(AppConstants.smallRadius),
                border: Border.all(
                  color: Theme.of(context).primaryColor.withOpacity(0.3),
                ),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(
                    _getFeatureIcon(feature),
                    size: 16,
                    color: Theme.of(context).primaryColor,
                  ),
                  const SizedBox(width: 6),
                  Text(
                    feature,
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: Theme.of(context).primaryColor,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            );
          }).toList(),
        ),
      ],
    );
  }

  IconData _getFeatureIcon(String feature) {
    final lowerFeature = feature.toLowerCase();
    
    if (lowerFeature.contains('مكيف') || lowerFeature.contains('تكييف')) {
      return Icons.ac_unit;
    } else if (lowerFeature.contains('تدفئة')) {
      return Icons.whatshot;
    } else if (lowerFeature.contains('إنترنت') || lowerFeature.contains('واي فاي')) {
      return Icons.wifi;
    } else if (lowerFeature.contains('أمان') || lowerFeature.contains('حراسة')) {
      return Icons.security;
    } else if (lowerFeature.contains('مدرسة') || lowerFeature.contains('تعليم')) {
      return Icons.school;
    } else if (lowerFeature.contains('مستشفى') || lowerFeature.contains('طبي')) {
      return Icons.local_hospital;
    } else if (lowerFeature.contains('تجاري') || lowerFeature.contains('مول')) {
      return Icons.shopping_cart;
    } else if (lowerFeature.contains('إطلالة') || lowerFeature.contains('منظر')) {
      return Icons.landscape;
    } else if (lowerFeature.contains('تشطيب') || lowerFeature.contains('فاخر')) {
      return Icons.star;
    } else if (lowerFeature.contains('مطبخ')) {
      return Icons.kitchen;
    } else if (lowerFeature.contains('شرفة') || lowerFeature.contains('بلكونة')) {
      return Icons.balcony;
    } else if (lowerFeature.contains('مصعد')) {
      return Icons.elevator;
    } else if (lowerFeature.contains('موقف') || lowerFeature.contains('جراج')) {
      return Icons.local_parking;
    } else if (lowerFeature.contains('حديقة')) {
      return Icons.grass;
    } else if (lowerFeature.contains('مسبح')) {
      return Icons.pool;
    } else {
      return Icons.check_circle_outline;
    }
  }
}
