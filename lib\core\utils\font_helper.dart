import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

class FontHelper {
  // Cache for loaded fonts to avoid repeated network calls
  static final Map<String, TextStyle> _fontCache = {};
  
  // Fallback font family
  static const String fallbackFontFamily = 'Arial';
  
  /// Safe method to get Google Font with fallback
  static TextStyle safeGoogleFont({
    required double fontSize,
    FontWeight? fontWeight,
    Color? color,
    String fontFamily = 'Cairo',
  }) {
    final cacheKey = '${fontFamily}_${fontSize}_${fontWeight?.index}_${color?.value}';
    
    // Return cached font if available
    if (_fontCache.containsKey(cacheKey)) {
      return _fontCache[cacheKey]!;
    }
    
    TextStyle textStyle;
    
    try {
      // Try to load Google Font
      switch (fontFamily.toLowerCase()) {
        case 'cairo':
          textStyle = GoogleFonts.cairo(
            fontSize: fontSize,
            fontWeight: fontWeight,
            color: color,
          );
          break;
        case 'amiri':
          textStyle = GoogleFonts.amiri(
            fontSize: fontSize,
            fontWeight: fontWeight,
            color: color,
          );
          break;
        case 'noto_sans_arabic':
          textStyle = GoogleFonts.notoSansArabic(
            fontSize: fontSize,
            fontWeight: fontWeight,
            color: color,
          );
          break;
        default:
          textStyle = GoogleFonts.cairo(
            fontSize: fontSize,
            fontWeight: fontWeight,
            color: color,
          );
      }
      
      // Cache the successful result
      _fontCache[cacheKey] = textStyle;
      
    } catch (e) {
      // Fallback to system font if Google Fonts fails
      textStyle = TextStyle(
        fontSize: fontSize,
        fontWeight: fontWeight,
        color: color,
        fontFamily: fallbackFontFamily,
      );
      
      // Cache the fallback result
      _fontCache[cacheKey] = textStyle;
      
      // Log the error for debugging
      debugPrint('Google Fonts failed to load $fontFamily: $e');
    }
    
    return textStyle;
  }
  
  /// Get text theme with safe Google Fonts
  static TextTheme getSafeTextTheme({
    required Brightness brightness,
    String fontFamily = 'Cairo',
  }) {
    final primaryColor = brightness == Brightness.light 
        ? const Color(0xFF212121) 
        : Colors.white;
    final secondaryColor = brightness == Brightness.light 
        ? const Color(0xFF757575) 
        : const Color(0xFFB0B0B0);
    
    return TextTheme(
      displayLarge: safeGoogleFont(
        fontSize: 32,
        fontWeight: FontWeight.bold,
        color: primaryColor,
        fontFamily: fontFamily,
      ),
      displayMedium: safeGoogleFont(
        fontSize: 28,
        fontWeight: FontWeight.bold,
        color: primaryColor,
        fontFamily: fontFamily,
      ),
      headlineLarge: safeGoogleFont(
        fontSize: 24,
        fontWeight: FontWeight.w600,
        color: primaryColor,
        fontFamily: fontFamily,
      ),
      headlineMedium: safeGoogleFont(
        fontSize: 20,
        fontWeight: FontWeight.w600,
        color: primaryColor,
        fontFamily: fontFamily,
      ),
      titleLarge: safeGoogleFont(
        fontSize: 18,
        fontWeight: FontWeight.w500,
        color: primaryColor,
        fontFamily: fontFamily,
      ),
      titleMedium: safeGoogleFont(
        fontSize: 16,
        fontWeight: FontWeight.w500,
        color: primaryColor,
        fontFamily: fontFamily,
      ),
      bodyLarge: safeGoogleFont(
        fontSize: 16,
        color: primaryColor,
        fontFamily: fontFamily,
      ),
      bodyMedium: safeGoogleFont(
        fontSize: 14,
        color: secondaryColor,
        fontFamily: fontFamily,
      ),
      labelLarge: safeGoogleFont(
        fontSize: 14,
        fontWeight: FontWeight.w500,
        color: brightness == Brightness.light ? Colors.white : Colors.black,
        fontFamily: fontFamily,
      ),
    );
  }
  
  /// Clear font cache (useful for testing or memory management)
  static void clearCache() {
    _fontCache.clear();
  }
  
  /// Check if a font is cached
  static bool isFontCached(String fontFamily, double fontSize, FontWeight? fontWeight, Color? color) {
    final cacheKey = '${fontFamily}_${fontSize}_${fontWeight?.index}_${color?.value}';
    return _fontCache.containsKey(cacheKey);
  }
  
  /// Get cache size for debugging
  static int getCacheSize() {
    return _fontCache.length;
  }
}
