import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';

import '../../../../core/constants/app_constants.dart';
import '../../../../core/utils/formatters.dart';
import '../../providers/notifications_provider.dart';
import '../../models/notification_model.dart';
import '../widgets/notification_tile.dart';

class NotificationsPage extends ConsumerWidget {
  const NotificationsPage({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final notificationsState = ref.watch(notificationsProvider);
    final notificationsNotifier = ref.read(notificationsProvider.notifier);

    return Scaffold(
      appBar: AppBar(
        title: const Text('الإشعارات'),
        centerTitle: true,
        actions: [
          if (notificationsNotifier.unreadCount > 0)
            TextButton(
              onPressed: () {
                notificationsNotifier.markAllAsRead();
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('تم تحديد جميع الإشعارات كمقروءة'),
                    backgroundColor: Colors.green,
                  ),
                );
              },
              child: const Text('تحديد الكل كمقروء'),
            ),
          PopupMenuButton<String>(
            onSelected: (value) {
              switch (value) {
                case 'delete_read':
                  _showDeleteReadConfirmation(context, notificationsNotifier);
                  break;
                case 'settings':
                  _showNotificationSettings(context);
                  break;
              }
            },
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'delete_read',
                child: Row(
                  children: [
                    Icon(Icons.delete_sweep),
                    SizedBox(width: 8),
                    Text('حذف المقروءة'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: 'settings',
                child: Row(
                  children: [
                    Icon(Icons.settings),
                    SizedBox(width: 8),
                    Text('إعدادات الإشعارات'),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
      body: notificationsState.isLoading
          ? const Center(child: CircularProgressIndicator())
          : notificationsState.notifications.isEmpty
              ? _buildEmptyState(context, notificationsNotifier)
              : _buildNotificationsList(context, notificationsState, notificationsNotifier),
    );
  }

  Widget _buildEmptyState(BuildContext context, NotificationsNotifier notifier) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(
            Icons.notifications_none,
            size: 80,
            color: Colors.grey,
          ),
          const SizedBox(height: 16),
          const Text(
            'لا توجد إشعارات',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Colors.grey,
            ),
          ),
          const SizedBox(height: 8),
          const Text(
            'ستظهر إشعاراتك هنا',
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey,
            ),
          ),
          const SizedBox(height: 24),
          ElevatedButton.icon(
            onPressed: () {
              notifier.simulateNewNotification();
            },
            icon: const Icon(Icons.add),
            label: const Text('إضافة إشعار تجريبي'),
          ),
        ],
      ),
    );
  }

  Widget _buildNotificationsList(
    BuildContext context,
    NotificationsState state,
    NotificationsNotifier notifier,
  ) {
    final sortedNotifications = notifier.sortedNotifications;
    final unreadCount = notifier.unreadCount;

    return Column(
      children: [
        // Summary header
        if (unreadCount > 0)
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(AppConstants.defaultPadding),
            color: Theme.of(context).primaryColor.withOpacity(0.1),
            child: Text(
              'لديك $unreadCount إشعار غير مقروء',
              style: TextStyle(
                color: Theme.of(context).primaryColor,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),

        // Notifications list
        Expanded(
          child: RefreshIndicator(
            onRefresh: () async {
              // TODO: Implement refresh
            },
            child: ListView.builder(
              padding: const EdgeInsets.all(AppConstants.defaultPadding),
              itemCount: sortedNotifications.length,
              itemBuilder: (context, index) {
                final notification = sortedNotifications[index];
                return NotificationTile(
                  notification: notification,
                  onTap: () {
                    _handleNotificationTap(context, notification, notifier);
                  },
                  onDismiss: () {
                    notifier.deleteNotification(notification.id);
                  },
                );
              },
            ),
          ),
        ),
      ],
    );
  }

  void _handleNotificationTap(
    BuildContext context,
    NotificationModel notification,
    NotificationsNotifier notifier,
  ) {
    // Mark as read
    if (!notification.isRead) {
      notifier.markAsRead(notification.id);
    }

    // Handle navigation based on notification type
    switch (notification.type) {
      case NotificationType.propertyApproved:
      case NotificationType.propertyRejected:
      case NotificationType.priceUpdate:
      case NotificationType.favoritePropertyUpdate:
        if (notification.propertyId != null) {
          context.push('/property/${notification.propertyId}');
        }
        break;
      case NotificationType.newMessage:
      case NotificationType.newInquiry:
        context.push('/messages');
        break;
      case NotificationType.systemUpdate:
        _showSystemUpdateDialog(context);
        break;
      case NotificationType.general:
        // Show notification details
        _showNotificationDetails(context, notification);
        break;
    }
  }

  void _showDeleteReadConfirmation(
    BuildContext context,
    NotificationsNotifier notifier,
  ) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('حذف الإشعارات المقروءة'),
        content: const Text('هل أنت متأكد من حذف جميع الإشعارات المقروءة؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              notifier.deleteAllRead();
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('تم حذف الإشعارات المقروءة'),
                  backgroundColor: Colors.green,
                ),
              );
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
            child: const Text('حذف'),
          ),
        ],
      ),
    );
  }

  void _showNotificationSettings(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('إعدادات الإشعارات'),
        content: const Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            CheckboxListTile(
              title: Text('إشعارات الرسائل'),
              value: true,
              onChanged: null, // TODO: Implement
            ),
            CheckboxListTile(
              title: Text('إشعارات العقارات'),
              value: true,
              onChanged: null, // TODO: Implement
            ),
            CheckboxListTile(
              title: Text('إشعارات النظام'),
              value: true,
              onChanged: null, // TODO: Implement
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إغلاق'),
          ),
        ],
      ),
    );
  }

  void _showSystemUpdateDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تحديث النظام'),
        content: const Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('تم إضافة ميزات جديدة:'),
            SizedBox(height: 12),
            Text('• نظام رفع الصور المحسن'),
            Text('• إشعارات فورية'),
            Text('• تحسينات في الأداء'),
            Text('• إصلاح الأخطاء'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إغلاق'),
          ),
        ],
      ),
    );
  }

  void _showNotificationDetails(BuildContext context, NotificationModel notification) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(notification.title),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(notification.body),
            const SizedBox(height: 16),
            Text(
              'تاريخ الإشعار: ${Formatters.formatDateTime(notification.createdAt)}',
              style: const TextStyle(
                fontSize: 12,
                color: Colors.grey,
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إغلاق'),
          ),
        ],
      ),
    );
  }
}
