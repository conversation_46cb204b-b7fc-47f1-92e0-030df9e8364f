import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';

import '../../../../core/constants/app_constants.dart';
import '../../../../core/models/property_model.dart';
import '../../../explore/presentation/widgets/property_card.dart';
import '../../providers/properties_provider.dart';

class MyPropertiesPage extends ConsumerStatefulWidget {
  const MyPropertiesPage({super.key});

  @override
  ConsumerState<MyPropertiesPage> createState() => _MyPropertiesPageState();
}

class _MyPropertiesPageState extends ConsumerState<MyPropertiesPage>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final propertiesState = ref.watch(propertiesProvider);
    final myProperties = _getMyProperties(propertiesState.properties);

    return Scaffold(
      appBar: AppBar(
        title: const Text('عقاراتي'),
        centerTitle: true,
        actions: [
          IconButton(
            icon: const Icon(Icons.add),
            onPressed: () {
              context.push('/add-property');
            },
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          tabs: const [
            Tab(text: 'الكل'),
            Tab(text: 'للبيع'),
            Tab(text: 'للإيجار'),
            Tab(text: 'معلق'),
          ],
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          _buildPropertiesList(myProperties),
          _buildPropertiesList(_filterByStatus(myProperties, PropertyStatus.forSale)),
          _buildPropertiesList(_filterByStatus(myProperties, PropertyStatus.forRent)),
          _buildPropertiesList(_filterByStatus(myProperties, PropertyStatus.pending)),
        ],
      ),
    );
  }

  List<PropertyModel> _getMyProperties(List<PropertyModel> allProperties) {
    // Mock: filter properties by current user
    // In real app, this would filter by actual user ID
    return allProperties.take(3).toList(); // Show first 3 as user's properties
  }

  List<PropertyModel> _filterByStatus(List<PropertyModel> properties, PropertyStatus status) {
    return properties.where((property) => property.status == status).toList();
  }

  Widget _buildPropertiesList(List<PropertyModel> properties) {
    if (properties.isEmpty) {
      return _buildEmptyState();
    }

    return RefreshIndicator(
      onRefresh: () async {
        await ref.read(propertiesProvider.notifier).refreshProperties();
      },
      child: ListView.builder(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        itemCount: properties.length,
        itemBuilder: (context, index) {
          final property = properties[index];
          return Padding(
            padding: const EdgeInsets.only(bottom: 16),
            child: _buildMyPropertyCard(property),
          );
        },
      ),
    );
  }

  Widget _buildEmptyState() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.home_work_outlined,
            size: 80,
            color: Colors.grey,
          ),
          SizedBox(height: 16),
          Text(
            'لا توجد عقارات',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Colors.grey,
            ),
          ),
          SizedBox(height: 8),
          Text(
            'ابدأ بإضافة عقارك الأول',
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMyPropertyCard(PropertyModel property) {
    return Card(
      child: Column(
        children: [
          PropertyCard(
            property: property,
            onTap: () {
              context.push('/property/${property.id}');
            },
          ),

          // Action buttons
          Padding(
            padding: const EdgeInsets.all(12),
            child: Row(
              children: [
                Expanded(
                  child: OutlinedButton.icon(
                    onPressed: () => _editProperty(property),
                    icon: const Icon(Icons.edit),
                    label: const Text('تعديل'),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: OutlinedButton.icon(
                    onPressed: () => _shareProperty(property),
                    icon: const Icon(Icons.share),
                    label: const Text('مشاركة'),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: OutlinedButton.icon(
                    onPressed: () => _showPropertyStats(property),
                    icon: const Icon(Icons.analytics),
                    label: const Text('الإحصائيات'),
                  ),
                ),
              ],
            ),
          ),

          // Status and actions
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
            decoration: BoxDecoration(
              color: Colors.grey[50],
              borderRadius: const BorderRadius.only(
                bottomLeft: Radius.circular(8),
                bottomRight: Radius.circular(8),
              ),
            ),
            child: Row(
              children: [
                _buildStatusChip(property.status),
                const Spacer(),
                Text(
                  '${property.views} مشاهدة',
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey[600],
                  ),
                ),
                const SizedBox(width: 16),
                PopupMenuButton<String>(
                  onSelected: (value) => _handlePropertyAction(value, property),
                  itemBuilder: (context) => [
                    const PopupMenuItem(
                      value: 'promote',
                      child: Row(
                        children: [
                          Icon(Icons.trending_up),
                          SizedBox(width: 8),
                          Text('ترويج'),
                        ],
                      ),
                    ),
                    const PopupMenuItem(
                      value: 'duplicate',
                      child: Row(
                        children: [
                          Icon(Icons.copy),
                          SizedBox(width: 8),
                          Text('نسخ'),
                        ],
                      ),
                    ),
                    const PopupMenuItem(
                      value: 'archive',
                      child: Row(
                        children: [
                          Icon(Icons.archive),
                          SizedBox(width: 8),
                          Text('أرشفة'),
                        ],
                      ),
                    ),
                    const PopupMenuItem(
                      value: 'delete',
                      child: Row(
                        children: [
                          Icon(Icons.delete, color: Colors.red),
                          SizedBox(width: 8),
                          Text('حذف', style: TextStyle(color: Colors.red)),
                        ],
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatusChip(PropertyStatus status) {
    Color color;
    String text;

    switch (status) {
      case PropertyStatus.forSale:
        color = Colors.green;
        text = 'للبيع';
        break;
      case PropertyStatus.forRent:
        color = Colors.blue;
        text = 'للإيجار';
        break;
      case PropertyStatus.sold:
        color = Colors.grey;
        text = 'تم البيع';
        break;
      case PropertyStatus.rented:
        color = Colors.grey;
        text = 'تم الإيجار';
        break;
      case PropertyStatus.pending:
        color = Colors.orange;
        text = 'معلق';
        break;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Text(
        text,
        style: TextStyle(
          color: color,
          fontSize: 12,
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }

  void _editProperty(PropertyModel property) {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('سيتم إضافة تعديل العقارات قريباً'),
        backgroundColor: Colors.orange,
      ),
    );
  }

  void _shareProperty(PropertyModel property) {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('تم نسخ رابط العقار'),
        backgroundColor: Colors.green,
      ),
    );
  }

  void _showPropertyStats(PropertyModel property) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('إحصائيات العقار'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            _buildStatRow('المشاهدات', '${property.views}'),
            _buildStatRow('الاستفسارات', '12'),
            _buildStatRow('المفضلة', '8'),
            _buildStatRow('المشاركات', '5'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إغلاق'),
          ),
        ],
      ),
    );
  }

  Widget _buildStatRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(label),
          Text(
            value,
            style: const TextStyle(fontWeight: FontWeight.bold),
          ),
        ],
      ),
    );
  }

  void _handlePropertyAction(String action, PropertyModel property) {
    switch (action) {
      case 'promote':
        _promoteProperty(property);
        break;
      case 'duplicate':
        _duplicateProperty(property);
        break;
      case 'archive':
        _archiveProperty(property);
        break;
      case 'delete':
        _deleteProperty(property);
        break;
    }
  }

  void _promoteProperty(PropertyModel property) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('ترويج العقار'),
        content: const Text('سيتم إضافة نظام الترويج قريباً'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إغلاق'),
          ),
        ],
      ),
    );
  }

  void _duplicateProperty(PropertyModel property) {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('تم نسخ العقار كمسودة'),
        backgroundColor: Colors.green,
      ),
    );
  }

  void _archiveProperty(PropertyModel property) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('أرشفة العقار'),
        content: const Text('هل تريد أرشفة هذا العقار؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('تم أرشفة العقار'),
                  backgroundColor: Colors.green,
                ),
              );
            },
            child: const Text('أرشفة'),
          ),
        ],
      ),
    );
  }

  void _deleteProperty(PropertyModel property) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('حذف العقار'),
        content: const Text('هل أنت متأكد من حذف هذا العقار؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('تم حذف العقار'),
                  backgroundColor: Colors.red,
                ),
              );
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
            child: const Text('حذف'),
          ),
        ],
      ),
    );
  }
}
