import 'package:flutter/material.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:flutter/services.dart';

class UrlLauncherService {
  /// Make a phone call
  static Future<bool> makePhoneCall(String phoneNumber) async {
    final Uri phoneUri = Uri(scheme: 'tel', path: phoneNumber);
    
    try {
      if (await canLaunchUrl(phoneUri)) {
        return await launchUrl(phoneUri);
      } else {
        debugPrint('Cannot launch phone call to $phoneNumber');
        return false;
      }
    } catch (e) {
      debugPrint('Error making phone call: $e');
      return false;
    }
  }

  /// Send SMS
  static Future<bool> sendSMS(String phoneNumber, {String? message}) async {
    final Uri smsUri = Uri(
      scheme: 'sms',
      path: phoneNumber,
      queryParameters: message != null ? {'body': message} : null,
    );
    
    try {
      if (await canLaunchUrl(smsUri)) {
        return await launchUrl(smsUri);
      } else {
        debugPrint('Cannot launch SMS to $phoneNumber');
        return false;
      }
    } catch (e) {
      debugPrint('Error sending SMS: $e');
      return false;
    }
  }

  /// Send email
  static Future<bool> sendEmail({
    required String email,
    String? subject,
    String? body,
  }) async {
    final Uri emailUri = Uri(
      scheme: 'mailto',
      path: email,
      queryParameters: {
        if (subject != null) 'subject': subject,
        if (body != null) 'body': body,
      },
    );
    
    try {
      if (await canLaunchUrl(emailUri)) {
        return await launchUrl(emailUri);
      } else {
        debugPrint('Cannot launch email to $email');
        return false;
      }
    } catch (e) {
      debugPrint('Error sending email: $e');
      return false;
    }
  }

  /// Open maps with location
  static Future<bool> openMaps({
    double? latitude,
    double? longitude,
    String? address,
  }) async {
    Uri mapsUri;
    
    if (latitude != null && longitude != null) {
      mapsUri = Uri.parse('https://www.google.com/maps/search/?api=1&query=$latitude,$longitude');
    } else if (address != null) {
      mapsUri = Uri.parse('https://www.google.com/maps/search/?api=1&query=${Uri.encodeComponent(address)}');
    } else {
      debugPrint('No location data provided for maps');
      return false;
    }
    
    try {
      if (await canLaunchUrl(mapsUri)) {
        return await launchUrl(mapsUri, mode: LaunchMode.externalApplication);
      } else {
        debugPrint('Cannot launch maps');
        return false;
      }
    } catch (e) {
      debugPrint('Error opening maps: $e');
      return false;
    }
  }

  /// Open WhatsApp chat
  static Future<bool> openWhatsApp(String phoneNumber, {String? message}) async {
    // Remove any non-digit characters and country code formatting
    final cleanNumber = phoneNumber.replaceAll(RegExp(r'[^\d]'), '');
    
    final Uri whatsappUri = Uri.parse(
      'https://wa.me/$cleanNumber${message != null ? '?text=${Uri.encodeComponent(message)}' : ''}',
    );
    
    try {
      if (await canLaunchUrl(whatsappUri)) {
        return await launchUrl(whatsappUri, mode: LaunchMode.externalApplication);
      } else {
        debugPrint('Cannot launch WhatsApp');
        return false;
      }
    } catch (e) {
      debugPrint('Error opening WhatsApp: $e');
      return false;
    }
  }

  /// Open website
  static Future<bool> openWebsite(String url) async {
    Uri uri;
    
    if (!url.startsWith('http://') && !url.startsWith('https://')) {
      uri = Uri.parse('https://$url');
    } else {
      uri = Uri.parse(url);
    }
    
    try {
      if (await canLaunchUrl(uri)) {
        return await launchUrl(uri, mode: LaunchMode.externalApplication);
      } else {
        debugPrint('Cannot launch website: $url');
        return false;
      }
    } catch (e) {
      debugPrint('Error opening website: $e');
      return false;
    }
  }

  /// Copy to clipboard
  static Future<void> copyToClipboard(String text) async {
    await Clipboard.setData(ClipboardData(text: text));
  }

  /// Show contact options dialog
  static Future<void> showContactDialog({
    required BuildContext context,
    required String name,
    String? phone,
    String? email,
    String? whatsapp,
  }) async {
    showModalBottomSheet(
      context: context,
      builder: (context) => SafeArea(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Padding(
              padding: const EdgeInsets.all(16),
              child: Text(
                'التواصل مع $name',
                style: const TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
            
            if (phone != null) ...[
              ListTile(
                leading: const Icon(Icons.call, color: Colors.green),
                title: const Text('اتصال هاتفي'),
                subtitle: Text(phone),
                onTap: () async {
                  Navigator.pop(context);
                  final success = await makePhoneCall(phone);
                  if (!success && context.mounted) {
                    await copyToClipboard(phone);
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(
                        content: Text('تم نسخ رقم الهاتف'),
                        backgroundColor: Colors.green,
                      ),
                    );
                  }
                },
              ),
              
              ListTile(
                leading: const Icon(Icons.message, color: Colors.blue),
                title: const Text('رسالة نصية'),
                subtitle: Text(phone),
                onTap: () async {
                  Navigator.pop(context);
                  final success = await sendSMS(phone);
                  if (!success && context.mounted) {
                    await copyToClipboard(phone);
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(
                        content: Text('تم نسخ رقم الهاتف'),
                        backgroundColor: Colors.green,
                      ),
                    );
                  }
                },
              ),
            ],
            
            if (whatsapp != null) 
              ListTile(
                leading: const Icon(Icons.chat, color: Colors.green),
                title: const Text('واتساب'),
                subtitle: Text(whatsapp),
                onTap: () async {
                  Navigator.pop(context);
                  await openWhatsApp(whatsapp);
                },
              ),
            
            if (email != null)
              ListTile(
                leading: const Icon(Icons.email, color: Colors.red),
                title: const Text('بريد إلكتروني'),
                subtitle: Text(email),
                onTap: () async {
                  Navigator.pop(context);
                  await sendEmail(email: email);
                },
              ),
          ],
        ),
      ),
    );
  }
}
