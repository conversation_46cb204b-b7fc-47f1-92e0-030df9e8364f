import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../../../core/constants/app_strings.dart';
import '../../../../core/constants/app_icons.dart';
import '../../../explore/presentation/pages/explore_page.dart';
import '../../../map/presentation/pages/map_page.dart';
import '../../../properties/presentation/pages/add_property_page.dart';
import '../../../favorites/presentation/pages/favorites_page.dart';
import '../../../profile/presentation/pages/profile_page.dart';

// Current page provider
final currentPageProvider = StateProvider<int>((ref) => 0);

class MainPage extends ConsumerWidget {
  const MainPage({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final currentIndex = ref.watch(currentPageProvider);

    final pages = [
      const ExplorePage(),
      const MapPage(),
      const AddPropertyPage(),
      const FavoritesPage(),
      const ProfilePage(),
    ];

    return Scaffold(
      body: IndexedStack(
        index: currentIndex,
        children: pages,
      ),
      bottomNavigationBar: BottomNavigationBar(
        type: BottomNavigationBarType.fixed,
        currentIndex: currentIndex,
        onTap: (index) => ref.read(currentPageProvider.notifier).state = index,
        selectedItemColor: Theme.of(context).primaryColor,
        unselectedItemColor: Colors.grey,
        items: const [
          BottomNavigationBarItem(
            icon: Icon(AppIcons.search),
            activeIcon: Icon(AppIcons.search),
            label: AppStrings.explore,
          ),
          BottomNavigationBarItem(
            icon: Icon(AppIcons.map),
            activeIcon: Icon(AppIcons.mapFilled),
            label: AppStrings.map,
          ),
          BottomNavigationBarItem(
            icon: Icon(AppIcons.add),
            activeIcon: Icon(AppIcons.addFilled),
            label: AppStrings.addProperty,
          ),
          BottomNavigationBarItem(
            icon: Icon(AppIcons.favorite),
            activeIcon: Icon(AppIcons.favoriteFilled),
            label: AppStrings.favorites,
          ),
          BottomNavigationBarItem(
            icon: Icon(AppIcons.profile),
            activeIcon: Icon(AppIcons.profileFilled),
            label: AppStrings.account,
          ),
        ],
      ),
    );
  }
}
