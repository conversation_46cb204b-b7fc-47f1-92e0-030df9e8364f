import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../models/message_model.dart';

@immutable
class MessagesState {
  final List<ConversationModel> conversations;
  final List<MessageModel> currentMessages;
  final bool isLoading;
  final String? error;
  final String? currentConversationId;

  const MessagesState({
    this.conversations = const [],
    this.currentMessages = const [],
    this.isLoading = false,
    this.error,
    this.currentConversationId,
  });

  MessagesState copyWith({
    List<ConversationModel>? conversations,
    List<MessageModel>? currentMessages,
    bool? isLoading,
    String? error,
    String? currentConversationId,
  }) {
    return MessagesState(
      conversations: conversations ?? this.conversations,
      currentMessages: currentMessages ?? this.currentMessages,
      isLoading: isLoading ?? this.isLoading,
      error: error,
      currentConversationId: currentConversationId ?? this.currentConversationId,
    );
  }
}

class MessagesNotifier extends StateNotifier<MessagesState> {
  MessagesNotifier() : super(const MessagesState()) {
    _loadConversations();
  }

  void _loadConversations() {
    // Simulate loading conversations
    final mockConversations = [
      ConversationModel(
        id: 'conv_1',
        userId1: 'user_1',
        userId2: 'user_2',
        propertyId: 'prop_1',
        lastMessage: MessageModel(
          id: 'msg_1',
          senderId: 'user_2',
          receiverId: 'user_1',
          content: 'مرحباً، أريد الاستفسار عن هذا العقار',
          createdAt: DateTime.now().subtract(const Duration(hours: 2)),
          type: MessageType.propertyInquiry,
        ),
        unreadCount: 1,
        createdAt: DateTime.now().subtract(const Duration(days: 1)),
        updatedAt: DateTime.now().subtract(const Duration(hours: 2)),
      ),
      ConversationModel(
        id: 'conv_2',
        userId1: 'user_1',
        userId2: 'user_3',
        lastMessage: MessageModel(
          id: 'msg_2',
          senderId: 'user_1',
          receiverId: 'user_3',
          content: 'شكراً لك على الرد السريع',
          createdAt: DateTime.now().subtract(const Duration(days: 1)),
        ),
        unreadCount: 0,
        createdAt: DateTime.now().subtract(const Duration(days: 2)),
        updatedAt: DateTime.now().subtract(const Duration(days: 1)),
      ),
    ];

    state = state.copyWith(conversations: mockConversations);
  }

  Future<void> sendMessage({
    required String receiverId,
    required String content,
    String? propertyId,
    MessageType type = MessageType.text,
  }) async {
    try {
      state = state.copyWith(isLoading: true, error: null);

      // Create new message
      final message = MessageModel(
        id: 'msg_${DateTime.now().millisecondsSinceEpoch}',
        senderId: 'current_user_id', // Should come from auth
        receiverId: receiverId,
        propertyId: propertyId,
        content: content,
        createdAt: DateTime.now(),
        type: type,
      );

      // Add to current messages
      final updatedMessages = [...state.currentMessages, message];

      // Update or create conversation
      final conversationId = _getOrCreateConversationId('current_user_id', receiverId);

      state = state.copyWith(
        currentMessages: updatedMessages,
        isLoading: false,
        currentConversationId: conversationId,
      );

      // Update conversations list
      _updateConversationWithNewMessage(message);

    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: 'فشل في إرسال الرسالة: $e',
      );
    }
  }

  void _updateConversationWithNewMessage(MessageModel message) {
    final conversations = [...state.conversations];
    final conversationIndex = conversations.indexWhere(
      (conv) => conv.id == state.currentConversationId,
    );

    if (conversationIndex != -1) {
      // Update existing conversation
      conversations[conversationIndex] = conversations[conversationIndex].copyWith(
        lastMessage: message,
        updatedAt: message.createdAt,
      );
    } else {
      // Create new conversation
      final newConversation = ConversationModel(
        id: state.currentConversationId!,
        userId1: message.senderId,
        userId2: message.receiverId,
        propertyId: message.propertyId,
        lastMessage: message,
        createdAt: message.createdAt,
        updatedAt: message.createdAt,
      );
      conversations.insert(0, newConversation);
    }

    state = state.copyWith(conversations: conversations);
  }

  String _getOrCreateConversationId(String userId1, String userId2) {
    // Find existing conversation
    final existingConversation = state.conversations.firstWhere(
      (conv) =>
        (conv.userId1 == userId1 && conv.userId2 == userId2) ||
        (conv.userId1 == userId2 && conv.userId2 == userId1),
      orElse: () => ConversationModel(
        id: '',
        userId1: '',
        userId2: '',
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      ),
    );

    if (existingConversation.id.isNotEmpty) {
      return existingConversation.id;
    }

    // Create new conversation ID
    final sortedIds = [userId1, userId2]..sort();
    return 'conv_${sortedIds[0]}_${sortedIds[1]}';
  }

  Future<void> loadConversation(String conversationId) async {
    try {
      state = state.copyWith(isLoading: true, error: null);

      // Simulate loading messages for conversation
      final mockMessages = [
        MessageModel(
          id: 'msg_1',
          senderId: 'user_2',
          receiverId: 'user_1',
          content: 'مرحباً، أريد الاستفسار عن هذا العقار',
          createdAt: DateTime.now().subtract(const Duration(hours: 3)),
          type: MessageType.propertyInquiry,
        ),
        MessageModel(
          id: 'msg_2',
          senderId: 'user_1',
          receiverId: 'user_2',
          content: 'أهلاً وسهلاً، كيف يمكنني مساعدتك؟',
          createdAt: DateTime.now().subtract(const Duration(hours: 2)),
        ),
        MessageModel(
          id: 'msg_3',
          senderId: 'user_2',
          receiverId: 'user_1',
          content: 'هل العقار متاح للمعاينة؟',
          createdAt: DateTime.now().subtract(const Duration(hours: 1)),
        ),
      ];

      state = state.copyWith(
        currentMessages: mockMessages,
        currentConversationId: conversationId,
        isLoading: false,
      );

    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: 'فشل في تحميل الرسائل: $e',
      );
    }
  }

  Future<void> markAsRead(String messageId) async {
    final updatedMessages = state.currentMessages.map((message) {
      if (message.id == messageId) {
        return message.copyWith(isRead: true);
      }
      return message;
    }).toList();

    state = state.copyWith(currentMessages: updatedMessages);
  }

  Future<void> markConversationAsRead(String conversationId) async {
    final updatedConversations = state.conversations.map((conversation) {
      if (conversation.id == conversationId) {
        return conversation.copyWith(unreadCount: 0);
      }
      return conversation;
    }).toList();

    state = state.copyWith(conversations: updatedConversations);
  }

  void clearError() {
    state = state.copyWith(error: null);
  }

  void clearCurrentConversation() {
    state = state.copyWith(
      currentMessages: [],
      currentConversationId: null,
    );
  }

  int get totalUnreadCount {
    return state.conversations.fold(0, (sum, conv) => sum + conv.unreadCount);
  }

  List<ConversationModel> get sortedConversations {
    final conversations = [...state.conversations];
    conversations.sort((a, b) => b.updatedAt.compareTo(a.updatedAt));
    return conversations;
  }
}

final messagesProvider = StateNotifierProvider<MessagesNotifier, MessagesState>(
  (ref) => MessagesNotifier(),
);
