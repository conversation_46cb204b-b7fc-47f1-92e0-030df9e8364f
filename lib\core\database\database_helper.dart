import 'dart:async';
import 'dart:convert';
import 'package:sqflite/sqflite.dart';
import 'package:path/path.dart';

import '../models/property_model.dart';
import '../models/user_model.dart';

class DatabaseHelper {
  static final DatabaseHelper _instance = DatabaseHelper._internal();
  static Database? _database;

  DatabaseHelper._internal();

  factory DatabaseHelper() => _instance;

  Future<Database> get database async {
    _database ??= await _initDatabase();
    return _database!;
  }

  Future<Database> _initDatabase() async {
    final String path = join(await getDatabasesPath(), 'real_estate.db');
    
    return await openDatabase(
      path,
      version: 1,
      onCreate: _onCreate,
      onUpgrade: _onUpgrade,
    );
  }

  Future<void> _onCreate(Database db, int version) async {
    // Users table
    await db.execute('''
      CREATE TABLE users (
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        email TEXT UNIQUE NOT NULL,
        phone TEXT,
        avatar TEXT,
        role TEXT NOT NULL,
        is_verified INTEGER NOT NULL DEFAULT 0,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL
      )
    ''');

    // Properties table
    await db.execute('''
      CREATE TABLE properties (
        id TEXT PRIMARY KEY,
        title TEXT NOT NULL,
        description TEXT NOT NULL,
        type TEXT NOT NULL,
        status TEXT NOT NULL,
        price REAL NOT NULL,
        currency TEXT NOT NULL DEFAULT 'YER',
        area REAL NOT NULL,
        bedrooms INTEGER NOT NULL DEFAULT 0,
        bathrooms INTEGER NOT NULL DEFAULT 0,
        has_parking INTEGER NOT NULL DEFAULT 0,
        has_elevator INTEGER NOT NULL DEFAULT 0,
        has_garden INTEGER NOT NULL DEFAULT 0,
        has_pool INTEGER NOT NULL DEFAULT 0,
        images TEXT, -- JSON array of image paths
        features TEXT, -- JSON array of features
        location_address TEXT NOT NULL,
        location_city TEXT NOT NULL,
        location_district TEXT NOT NULL,
        location_latitude REAL,
        location_longitude REAL,
        owner_id TEXT NOT NULL,
        is_verified INTEGER NOT NULL DEFAULT 0,
        views INTEGER NOT NULL DEFAULT 0,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL,
        FOREIGN KEY (owner_id) REFERENCES users (id)
      )
    ''');

    // Favorites table
    await db.execute('''
      CREATE TABLE favorites (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        user_id TEXT NOT NULL,
        property_id TEXT NOT NULL,
        created_at TEXT NOT NULL,
        FOREIGN KEY (user_id) REFERENCES users (id),
        FOREIGN KEY (property_id) REFERENCES properties (id),
        UNIQUE(user_id, property_id)
      )
    ''');

    // Messages table
    await db.execute('''
      CREATE TABLE messages (
        id TEXT PRIMARY KEY,
        sender_id TEXT NOT NULL,
        receiver_id TEXT NOT NULL,
        property_id TEXT,
        content TEXT NOT NULL,
        is_read INTEGER NOT NULL DEFAULT 0,
        created_at TEXT NOT NULL,
        FOREIGN KEY (sender_id) REFERENCES users (id),
        FOREIGN KEY (receiver_id) REFERENCES users (id),
        FOREIGN KEY (property_id) REFERENCES properties (id)
      )
    ''');

    // Search history table
    await db.execute('''
      CREATE TABLE search_history (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        user_id TEXT,
        query TEXT NOT NULL,
        created_at TEXT NOT NULL,
        FOREIGN KEY (user_id) REFERENCES users (id)
      )
    ''');

    // Property views table
    await db.execute('''
      CREATE TABLE property_views (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        property_id TEXT NOT NULL,
        user_id TEXT,
        ip_address TEXT,
        created_at TEXT NOT NULL,
        FOREIGN KEY (property_id) REFERENCES properties (id),
        FOREIGN KEY (user_id) REFERENCES users (id)
      )
    ''');
  }

  Future<void> _onUpgrade(Database db, int oldVersion, int newVersion) async {
    // Handle database upgrades here
    if (oldVersion < 2) {
      // Add new columns or tables for version 2
    }
  }

  // User operations
  Future<int> insertUser(UserModel user) async {
    final db = await database;
    return await db.insert(
      'users',
      _userToMap(user),
      conflictAlgorithm: ConflictAlgorithm.replace,
    );
  }

  Future<UserModel?> getUser(String id) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'users',
      where: 'id = ?',
      whereArgs: [id],
    );

    if (maps.isNotEmpty) {
      return _mapToUser(maps.first);
    }
    return null;
  }

  Future<UserModel?> getUserByEmail(String email) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'users',
      where: 'email = ?',
      whereArgs: [email],
    );

    if (maps.isNotEmpty) {
      return _mapToUser(maps.first);
    }
    return null;
  }

  Future<int> updateUser(UserModel user) async {
    final db = await database;
    return await db.update(
      'users',
      _userToMap(user),
      where: 'id = ?',
      whereArgs: [user.id],
    );
  }

  // Property operations
  Future<int> insertProperty(PropertyModel property) async {
    final db = await database;
    return await db.insert(
      'properties',
      _propertyToMap(property),
      conflictAlgorithm: ConflictAlgorithm.replace,
    );
  }

  Future<List<PropertyModel>> getAllProperties() async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'properties',
      orderBy: 'created_at DESC',
    );

    return maps.map((map) => _mapToProperty(map)).toList();
  }

  Future<PropertyModel?> getProperty(String id) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'properties',
      where: 'id = ?',
      whereArgs: [id],
    );

    if (maps.isNotEmpty) {
      return _mapToProperty(maps.first);
    }
    return null;
  }

  Future<List<PropertyModel>> searchProperties(String query) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'properties',
      where: 'title LIKE ? OR description LIKE ? OR location_address LIKE ?',
      whereArgs: ['%$query%', '%$query%', '%$query%'],
      orderBy: 'created_at DESC',
    );

    return maps.map((map) => _mapToProperty(map)).toList();
  }

  Future<int> updateProperty(PropertyModel property) async {
    final db = await database;
    return await db.update(
      'properties',
      _propertyToMap(property),
      where: 'id = ?',
      whereArgs: [property.id],
    );
  }

  Future<int> deleteProperty(String id) async {
    final db = await database;
    return await db.delete(
      'properties',
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  // Favorites operations
  Future<int> addToFavorites(String userId, String propertyId) async {
    final db = await database;
    return await db.insert(
      'favorites',
      {
        'user_id': userId,
        'property_id': propertyId,
        'created_at': DateTime.now().toIso8601String(),
      },
      conflictAlgorithm: ConflictAlgorithm.ignore,
    );
  }

  Future<int> removeFromFavorites(String userId, String propertyId) async {
    final db = await database;
    return await db.delete(
      'favorites',
      where: 'user_id = ? AND property_id = ?',
      whereArgs: [userId, propertyId],
    );
  }

  Future<List<String>> getUserFavorites(String userId) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'favorites',
      columns: ['property_id'],
      where: 'user_id = ?',
      whereArgs: [userId],
    );

    return maps.map((map) => map['property_id'] as String).toList();
  }

  Future<bool> isFavorite(String userId, String propertyId) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'favorites',
      where: 'user_id = ? AND property_id = ?',
      whereArgs: [userId, propertyId],
    );

    return maps.isNotEmpty;
  }

  // Helper methods
  Map<String, dynamic> _userToMap(UserModel user) {
    return {
      'id': user.id,
      'name': user.name,
      'email': user.email,
      'phone': user.phone,
      'avatar': user.avatar,
      'role': user.role.name,
      'is_verified': user.isVerified ? 1 : 0,
      'created_at': user.createdAt.toIso8601String(),
      'updated_at': DateTime.now().toIso8601String(),
    };
  }

  UserModel _mapToUser(Map<String, dynamic> map) {
    return UserModel(
      id: map['id'],
      name: map['name'],
      email: map['email'],
      phone: map['phone'],
      avatar: map['avatar'],
      role: UserRole.values.firstWhere(
        (role) => role.name == map['role'],
        orElse: () => UserRole.user,
      ),
      isVerified: map['is_verified'] == 1,
      createdAt: DateTime.parse(map['created_at']),
    );
  }

  Map<String, dynamic> _propertyToMap(PropertyModel property) {
    return {
      'id': property.id,
      'title': property.title,
      'description': property.description,
      'type': property.type.name,
      'status': property.status.name,
      'price': property.price,
      'currency': property.currency,
      'area': property.area,
      'bedrooms': property.bedrooms,
      'bathrooms': property.bathrooms,
      'has_parking': property.hasParking ? 1 : 0,
      'has_elevator': property.hasElevator ? 1 : 0,
      'has_garden': property.hasGarden ? 1 : 0,
      'has_pool': property.hasPool ? 1 : 0,
      'images': jsonEncode(property.images),
      'features': jsonEncode(property.features),
      'location_address': property.location.address,
      'location_city': property.location.city,
      'location_district': property.location.district,
      'location_latitude': property.location.latitude,
      'location_longitude': property.location.longitude,
      'owner_id': property.ownerId,
      'is_verified': property.isVerified ? 1 : 0,
      'views': property.views,
      'created_at': property.createdAt.toIso8601String(),
      'updated_at': DateTime.now().toIso8601String(),
    };
  }

  PropertyModel _mapToProperty(Map<String, dynamic> map) {
    return PropertyModel(
      id: map['id'],
      title: map['title'],
      description: map['description'],
      type: PropertyType.values.firstWhere(
        (type) => type.name == map['type'],
        orElse: () => PropertyType.apartment,
      ),
      status: PropertyStatus.values.firstWhere(
        (status) => status.name == map['status'],
        orElse: () => PropertyStatus.forSale,
      ),
      price: map['price'],
      currency: map['currency'],
      area: map['area'],
      bedrooms: map['bedrooms'],
      bathrooms: map['bathrooms'],
      hasParking: map['has_parking'] == 1,
      hasElevator: map['has_elevator'] == 1,
      hasGarden: map['has_garden'] == 1,
      hasPool: map['has_pool'] == 1,
      images: List<String>.from(jsonDecode(map['images'] ?? '[]')),
      features: List<String>.from(jsonDecode(map['features'] ?? '[]')),
      location: LocationModel(
        address: map['location_address'],
        city: map['location_city'],
        district: map['location_district'],
        latitude: map['location_latitude'],
        longitude: map['location_longitude'],
      ),
      ownerId: map['owner_id'],
      isVerified: map['is_verified'] == 1,
      views: map['views'],
      createdAt: DateTime.parse(map['created_at']),
    );
  }

  // Close database
  Future<void> close() async {
    final db = await database;
    await db.close();
  }
}
