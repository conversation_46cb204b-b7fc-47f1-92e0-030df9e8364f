import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter/foundation.dart';

import '../models/notification_model.dart';

@immutable
class NotificationsState {
  final List<NotificationModel> notifications;
  final bool isLoading;
  final String? error;

  const NotificationsState({
    this.notifications = const [],
    this.isLoading = false,
    this.error,
  });

  NotificationsState copyWith({
    List<NotificationModel>? notifications,
    bool? isLoading,
    String? error,
  }) {
    return NotificationsState(
      notifications: notifications ?? this.notifications,
      isLoading: isLoading ?? this.isLoading,
      error: error,
    );
  }
}

class NotificationsNotifier extends StateNotifier<NotificationsState> {
  NotificationsNotifier() : super(const NotificationsState()) {
    _loadNotifications();
  }

  void _loadNotifications() {
    // Simulate loading notifications
    final mockNotifications = [
      NotificationModel(
        id: 'notif_1',
        title: 'تم قبول عقارك',
        body: 'تم قبول عقار "شقة فاخرة في حي الصافية" ونشره في التطبيق',
        type: NotificationType.propertyApproved,
        propertyId: 'prop_1',
        createdAt: DateTime.now().subtract(const Duration(hours: 2)),
      ),
      NotificationModel(
        id: 'notif_2',
        title: 'رسالة جديدة',
        body: 'لديك رسالة جديدة من أحمد محمد حول عقارك',
        type: NotificationType.newMessage,
        propertyId: 'prop_2',
        createdAt: DateTime.now().subtract(const Duration(hours: 5)),
      ),
      NotificationModel(
        id: 'notif_3',
        title: 'استفسار جديد',
        body: 'لديك استفسار جديد حول عقار "فيلا مع حديقة"',
        type: NotificationType.newInquiry,
        propertyId: 'prop_3',
        createdAt: DateTime.now().subtract(const Duration(days: 1)),
      ),
      NotificationModel(
        id: 'notif_4',
        title: 'تحديث السعر',
        body: 'تم تحديث سعر أحد العقارات في قائمة المفضلة لديك',
        type: NotificationType.favoritePropertyUpdate,
        propertyId: 'prop_4',
        createdAt: DateTime.now().subtract(const Duration(days: 2)),
      ),
      NotificationModel(
        id: 'notif_5',
        title: 'تحديث التطبيق',
        body: 'تم إضافة ميزات جديدة للتطبيق. قم بتحديث التطبيق للاستفادة منها',
        type: NotificationType.systemUpdate,
        createdAt: DateTime.now().subtract(const Duration(days: 3)),
      ),
    ];

    state = state.copyWith(notifications: mockNotifications);
  }

  Future<void> markAsRead(String notificationId) async {
    final updatedNotifications = state.notifications.map((notification) {
      if (notification.id == notificationId) {
        return notification.copyWith(isRead: true);
      }
      return notification;
    }).toList();

    state = state.copyWith(notifications: updatedNotifications);
  }

  Future<void> markAllAsRead() async {
    final updatedNotifications = state.notifications.map((notification) {
      return notification.copyWith(isRead: true);
    }).toList();

    state = state.copyWith(notifications: updatedNotifications);
  }

  Future<void> deleteNotification(String notificationId) async {
    final updatedNotifications = state.notifications
        .where((notification) => notification.id != notificationId)
        .toList();

    state = state.copyWith(notifications: updatedNotifications);
  }

  Future<void> deleteAllRead() async {
    final updatedNotifications = state.notifications
        .where((notification) => !notification.isRead)
        .toList();

    state = state.copyWith(notifications: updatedNotifications);
  }

  Future<void> addNotification(NotificationModel notification) async {
    final updatedNotifications = [notification, ...state.notifications];
    state = state.copyWith(notifications: updatedNotifications);
  }

  void clearError() {
    state = state.copyWith(error: null);
  }

  // Getters
  int get unreadCount {
    return state.notifications.where((notif) => !notif.isRead).length;
  }

  List<NotificationModel> get unreadNotifications {
    return state.notifications.where((notif) => !notif.isRead).toList();
  }

  List<NotificationModel> get readNotifications {
    return state.notifications.where((notif) => notif.isRead).toList();
  }

  List<NotificationModel> get sortedNotifications {
    final notifications = [...state.notifications];
    notifications.sort((a, b) => b.createdAt.compareTo(a.createdAt));
    return notifications;
  }

  List<NotificationModel> getNotificationsByType(NotificationType type) {
    return state.notifications.where((notif) => notif.type == type).toList();
  }

  // Simulate real-time notifications
  void simulateNewNotification() {
    final newNotification = NotificationModel(
      id: 'notif_${DateTime.now().millisecondsSinceEpoch}',
      title: 'إشعار جديد',
      body: 'لديك إشعار جديد في التطبيق',
      type: NotificationType.general,
      createdAt: DateTime.now(),
    );

    addNotification(newNotification);
  }
}

final notificationsProvider = StateNotifierProvider<NotificationsNotifier, NotificationsState>(
  (ref) => NotificationsNotifier(),
);
