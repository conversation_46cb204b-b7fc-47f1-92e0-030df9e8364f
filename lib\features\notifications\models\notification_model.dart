import 'package:flutter/material.dart';

@immutable
class NotificationModel {
  final String id;
  final String title;
  final String body;
  final NotificationType type;
  final String? userId;
  final String? propertyId;
  final String? actionData;
  final bool isRead;
  final DateTime createdAt;

  const NotificationModel({
    required this.id,
    required this.title,
    required this.body,
    required this.type,
    this.userId,
    this.propertyId,
    this.actionData,
    this.isRead = false,
    required this.createdAt,
  });

  NotificationModel copyWith({
    String? id,
    String? title,
    String? body,
    NotificationType? type,
    String? userId,
    String? propertyId,
    String? actionData,
    bool? isRead,
    DateTime? createdAt,
  }) {
    return NotificationModel(
      id: id ?? this.id,
      title: title ?? this.title,
      body: body ?? this.body,
      type: type ?? this.type,
      userId: userId ?? this.userId,
      propertyId: propertyId ?? this.propertyId,
      actionData: actionData ?? this.actionData,
      isRead: isRead ?? this.isRead,
      createdAt: createdAt ?? this.createdAt,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'body': body,
      'type': type.name,
      'userId': userId,
      'propertyId': propertyId,
      'actionData': actionData,
      'isRead': isRead,
      'createdAt': createdAt.toIso8601String(),
    };
  }

  factory NotificationModel.fromJson(Map<String, dynamic> json) {
    return NotificationModel(
      id: json['id'],
      title: json['title'],
      body: json['body'],
      type: NotificationType.values.firstWhere(
        (type) => type.name == json['type'],
        orElse: () => NotificationType.general,
      ),
      userId: json['userId'],
      propertyId: json['propertyId'],
      actionData: json['actionData'],
      isRead: json['isRead'] ?? false,
      createdAt: DateTime.parse(json['createdAt']),
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is NotificationModel && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}

enum NotificationType {
  general,
  propertyApproved,
  propertyRejected,
  newMessage,
  newInquiry,
  priceUpdate,
  favoritePropertyUpdate,
  systemUpdate,
}

extension NotificationTypeExtension on NotificationType {
  String get displayName {
    switch (this) {
      case NotificationType.general:
        return 'عام';
      case NotificationType.propertyApproved:
        return 'تم قبول العقار';
      case NotificationType.propertyRejected:
        return 'تم رفض العقار';
      case NotificationType.newMessage:
        return 'رسالة جديدة';
      case NotificationType.newInquiry:
        return 'استفسار جديد';
      case NotificationType.priceUpdate:
        return 'تحديث السعر';
      case NotificationType.favoritePropertyUpdate:
        return 'تحديث عقار مفضل';
      case NotificationType.systemUpdate:
        return 'تحديث النظام';
    }
  }

  IconData get icon {
    switch (this) {
      case NotificationType.general:
        return Icons.notifications;
      case NotificationType.propertyApproved:
        return Icons.check_circle;
      case NotificationType.propertyRejected:
        return Icons.cancel;
      case NotificationType.newMessage:
        return Icons.message;
      case NotificationType.newInquiry:
        return Icons.help_outline;
      case NotificationType.priceUpdate:
        return Icons.trending_up;
      case NotificationType.favoritePropertyUpdate:
        return Icons.favorite;
      case NotificationType.systemUpdate:
        return Icons.system_update;
    }
  }

  Color get color {
    switch (this) {
      case NotificationType.general:
        return Colors.blue;
      case NotificationType.propertyApproved:
        return Colors.green;
      case NotificationType.propertyRejected:
        return Colors.red;
      case NotificationType.newMessage:
        return Colors.purple;
      case NotificationType.newInquiry:
        return Colors.orange;
      case NotificationType.priceUpdate:
        return Colors.teal;
      case NotificationType.favoritePropertyUpdate:
        return Colors.pink;
      case NotificationType.systemUpdate:
        return Colors.indigo;
    }
  }
}
