import 'user_model.dart';

class PropertyModel {
  final String id;
  final String title;
  final String description;
  final PropertyType type;
  final PropertyStatus status;
  final double price;
  final String currency;
  final double area;
  final int bedrooms;
  final int bathrooms;
  final bool hasParking;
  final bool hasElevator;
  final bool hasGarden;
  final bool hasPool;
  final List<String> images;
  final LocationModel location;
  final String ownerId;
  final UserModel? owner;
  final bool isVerified;
  final bool isFeatured;
  final DateTime createdAt;
  final DateTime? updatedAt;
  final List<String> features;
  final int views;
  final int favorites;

  const PropertyModel({
    required this.id,
    required this.title,
    required this.description,
    required this.type,
    required this.status,
    required this.price,
    this.currency = 'YER',
    required this.area,
    this.bedrooms = 0,
    this.bathrooms = 0,
    this.hasParking = false,
    this.hasElevator = false,
    this.hasGarden = false,
    this.hasPool = false,
    this.images = const [],
    required this.location,
    required this.ownerId,
    this.owner,
    this.isVerified = false,
    this.isFeatured = false,
    required this.createdAt,
    this.updatedAt,
    this.features = const [],
    this.views = 0,
    this.favorites = 0,
  });

  factory PropertyModel.fromJson(Map<String, dynamic> json) {
    return PropertyModel(
      id: json['id'] as String,
      title: json['title'] as String,
      description: json['description'] as String,
      type: PropertyType.values.firstWhere(
        (type) => type.name == json['type'],
        orElse: () => PropertyType.apartment,
      ),
      status: PropertyStatus.values.firstWhere(
        (status) => status.name == json['status'],
        orElse: () => PropertyStatus.forSale,
      ),
      price: (json['price'] as num).toDouble(),
      currency: json['currency'] as String? ?? 'YER',
      area: (json['area'] as num).toDouble(),
      bedrooms: json['bedrooms'] as int? ?? 0,
      bathrooms: json['bathrooms'] as int? ?? 0,
      hasParking: json['has_parking'] as bool? ?? false,
      hasElevator: json['has_elevator'] as bool? ?? false,
      hasGarden: json['has_garden'] as bool? ?? false,
      hasPool: json['has_pool'] as bool? ?? false,
      images: List<String>.from(json['images'] as List? ?? []),
      location: LocationModel.fromJson(json['location'] as Map<String, dynamic>),
      ownerId: json['owner_id'] as String,
      owner: json['owner'] != null
          ? UserModel.fromJson(json['owner'] as Map<String, dynamic>)
          : null,
      isVerified: json['is_verified'] as bool? ?? false,
      isFeatured: json['is_featured'] as bool? ?? false,
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: json['updated_at'] != null
          ? DateTime.parse(json['updated_at'] as String)
          : null,
      features: List<String>.from(json['features'] as List? ?? []),
      views: json['views'] as int? ?? 0,
      favorites: json['favorites'] as int? ?? 0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'description': description,
      'type': type.name,
      'status': status.name,
      'price': price,
      'currency': currency,
      'area': area,
      'bedrooms': bedrooms,
      'bathrooms': bathrooms,
      'has_parking': hasParking,
      'has_elevator': hasElevator,
      'has_garden': hasGarden,
      'has_pool': hasPool,
      'images': images,
      'location': location.toJson(),
      'owner_id': ownerId,
      'owner': owner?.toJson(),
      'is_verified': isVerified,
      'is_featured': isFeatured,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
      'features': features,
      'views': views,
      'favorites': favorites,
    };
  }

  PropertyModel copyWith({
    String? id,
    String? title,
    String? description,
    PropertyType? type,
    PropertyStatus? status,
    double? price,
    String? currency,
    double? area,
    int? bedrooms,
    int? bathrooms,
    bool? hasParking,
    bool? hasElevator,
    bool? hasGarden,
    bool? hasPool,
    List<String>? images,
    LocationModel? location,
    String? ownerId,
    UserModel? owner,
    bool? isVerified,
    bool? isFeatured,
    DateTime? createdAt,
    DateTime? updatedAt,
    List<String>? features,
    int? views,
    int? favorites,
  }) {
    return PropertyModel(
      id: id ?? this.id,
      title: title ?? this.title,
      description: description ?? this.description,
      type: type ?? this.type,
      status: status ?? this.status,
      price: price ?? this.price,
      currency: currency ?? this.currency,
      area: area ?? this.area,
      bedrooms: bedrooms ?? this.bedrooms,
      bathrooms: bathrooms ?? this.bathrooms,
      hasParking: hasParking ?? this.hasParking,
      hasElevator: hasElevator ?? this.hasElevator,
      hasGarden: hasGarden ?? this.hasGarden,
      hasPool: hasPool ?? this.hasPool,
      images: images ?? this.images,
      location: location ?? this.location,
      ownerId: ownerId ?? this.ownerId,
      owner: owner ?? this.owner,
      isVerified: isVerified ?? this.isVerified,
      isFeatured: isFeatured ?? this.isFeatured,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      features: features ?? this.features,
      views: views ?? this.views,
      favorites: favorites ?? this.favorites,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is PropertyModel && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}

enum PropertyType {
  apartment('apartment'),
  villa('villa'),
  house('house'),
  office('office'),
  shop('shop'),
  warehouse('warehouse'),
  land('land');

  const PropertyType(this.name);
  final String name;

  String get displayName {
    switch (this) {
      case PropertyType.apartment:
        return 'شقة';
      case PropertyType.villa:
        return 'فيلا';
      case PropertyType.house:
        return 'منزل';
      case PropertyType.office:
        return 'مكتب';
      case PropertyType.shop:
        return 'محل';
      case PropertyType.warehouse:
        return 'مستودع';
      case PropertyType.land:
        return 'أرض';
    }
  }
}

enum PropertyStatus {
  forSale('for_sale'),
  forRent('for_rent'),
  sold('sold'),
  rented('rented'),
  pending('pending');

  const PropertyStatus(this.name);
  final String name;

  String get displayName {
    switch (this) {
      case PropertyStatus.forSale:
        return 'للبيع';
      case PropertyStatus.forRent:
        return 'للإيجار';
      case PropertyStatus.sold:
        return 'تم البيع';
      case PropertyStatus.rented:
        return 'تم التأجير';
      case PropertyStatus.pending:
        return 'قيد المراجعة';
    }
  }
}

class LocationModel {
  final String address;
  final String city;
  final String district;
  final double? latitude;
  final double? longitude;

  const LocationModel({
    required this.address,
    required this.city,
    required this.district,
    this.latitude,
    this.longitude,
  });

  factory LocationModel.fromJson(Map<String, dynamic> json) {
    return LocationModel(
      address: json['address'] as String,
      city: json['city'] as String,
      district: json['district'] as String,
      latitude: json['latitude'] as double?,
      longitude: json['longitude'] as double?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'address': address,
      'city': city,
      'district': district,
      'latitude': latitude,
      'longitude': longitude,
    };
  }

  LocationModel copyWith({
    String? address,
    String? city,
    String? district,
    double? latitude,
    double? longitude,
  }) {
    return LocationModel(
      address: address ?? this.address,
      city: city ?? this.city,
      district: district ?? this.district,
      latitude: latitude ?? this.latitude,
      longitude: longitude ?? this.longitude,
    );
  }
}
