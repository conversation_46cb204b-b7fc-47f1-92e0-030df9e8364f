import 'package:flutter/material.dart';

@immutable
class MessageModel {
  final String id;
  final String senderId;
  final String receiverId;
  final String? propertyId;
  final String content;
  final bool isRead;
  final DateTime createdAt;
  final MessageType type;

  const MessageModel({
    required this.id,
    required this.senderId,
    required this.receiverId,
    this.propertyId,
    required this.content,
    this.isRead = false,
    required this.createdAt,
    this.type = MessageType.text,
  });

  MessageModel copyWith({
    String? id,
    String? senderId,
    String? receiverId,
    String? propertyId,
    String? content,
    bool? isRead,
    DateTime? createdAt,
    MessageType? type,
  }) {
    return MessageModel(
      id: id ?? this.id,
      senderId: senderId ?? this.senderId,
      receiverId: receiverId ?? this.receiverId,
      propertyId: propertyId ?? this.propertyId,
      content: content ?? this.content,
      isRead: isRead ?? this.isRead,
      createdAt: createdAt ?? this.createdAt,
      type: type ?? this.type,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'senderId': senderId,
      'receiverId': receiverId,
      'propertyId': propertyId,
      'content': content,
      'isRead': isRead,
      'createdAt': createdAt.toIso8601String(),
      'type': type.name,
    };
  }

  factory MessageModel.fromJson(Map<String, dynamic> json) {
    return MessageModel(
      id: json['id'],
      senderId: json['senderId'],
      receiverId: json['receiverId'],
      propertyId: json['propertyId'],
      content: json['content'],
      isRead: json['isRead'] ?? false,
      createdAt: DateTime.parse(json['createdAt']),
      type: MessageType.values.firstWhere(
        (type) => type.name == json['type'],
        orElse: () => MessageType.text,
      ),
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is MessageModel && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'MessageModel(id: $id, senderId: $senderId, receiverId: $receiverId, content: $content)';
  }
}

enum MessageType {
  text,
  image,
  propertyInquiry,
  system,
}

extension MessageTypeExtension on MessageType {
  String get displayName {
    switch (this) {
      case MessageType.text:
        return 'نص';
      case MessageType.image:
        return 'صورة';
      case MessageType.propertyInquiry:
        return 'استفسار عقار';
      case MessageType.system:
        return 'نظام';
    }
  }

  IconData get icon {
    switch (this) {
      case MessageType.text:
        return Icons.message;
      case MessageType.image:
        return Icons.image;
      case MessageType.propertyInquiry:
        return Icons.home;
      case MessageType.system:
        return Icons.info;
    }
  }
}

@immutable
class ConversationModel {
  final String id;
  final String userId1;
  final String userId2;
  final String? propertyId;
  final MessageModel? lastMessage;
  final int unreadCount;
  final DateTime createdAt;
  final DateTime updatedAt;

  const ConversationModel({
    required this.id,
    required this.userId1,
    required this.userId2,
    this.propertyId,
    this.lastMessage,
    this.unreadCount = 0,
    required this.createdAt,
    required this.updatedAt,
  });

  ConversationModel copyWith({
    String? id,
    String? userId1,
    String? userId2,
    String? propertyId,
    MessageModel? lastMessage,
    int? unreadCount,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return ConversationModel(
      id: id ?? this.id,
      userId1: userId1 ?? this.userId1,
      userId2: userId2 ?? this.userId2,
      propertyId: propertyId ?? this.propertyId,
      lastMessage: lastMessage ?? this.lastMessage,
      unreadCount: unreadCount ?? this.unreadCount,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  String getOtherUserId(String currentUserId) {
    return userId1 == currentUserId ? userId2 : userId1;
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is ConversationModel && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}
