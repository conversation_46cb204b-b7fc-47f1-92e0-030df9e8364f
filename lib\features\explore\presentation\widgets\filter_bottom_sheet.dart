import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../../../core/constants/app_constants.dart';
import '../../../../core/models/property_model.dart';
import '../../../properties/providers/properties_provider.dart';

class FilterBottomSheet extends ConsumerStatefulWidget {
  const FilterBottomSheet({super.key});

  @override
  ConsumerState<FilterBottomSheet> createState() => _FilterBottomSheetState();
}

class _FilterBottomSheetState extends ConsumerState<FilterBottomSheet> {
  late PropertyFilter _filter;

  @override
  void initState() {
    super.initState();
    _filter = ref.read(propertiesProvider).filter;
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      decoration: const BoxDecoration(
        borderRadius: BorderRadius.vertical(
          top: Radius.circular(AppConstants.largeRadius),
        ),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'فلترة النتائج',
                style: Theme.of(context).textTheme.headlineSmall,
              ),
              IconButton(
                icon: const Icon(Icons.close),
                onPressed: () => Navigator.pop(context),
              ),
            ],
          ),
          
          const SizedBox(height: 16),
          
          // Property Type
          Text(
            'نوع العقار',
            style: Theme.of(context).textTheme.titleMedium,
          ),
          const SizedBox(height: 8),
          Wrap(
            spacing: 8,
            children: PropertyType.values.map((type) {
              return FilterChip(
                label: Text(type.displayName),
                selected: _filter.type == type,
                onSelected: (selected) {
                  setState(() {
                    _filter = _filter.copyWith(
                      type: selected ? type : null,
                    );
                  });
                },
              );
            }).toList(),
          ),
          
          const SizedBox(height: 16),
          
          // Property Status
          Text(
            'حالة العقار',
            style: Theme.of(context).textTheme.titleMedium,
          ),
          const SizedBox(height: 8),
          Wrap(
            spacing: 8,
            children: [PropertyStatus.forSale, PropertyStatus.forRent].map((status) {
              return FilterChip(
                label: Text(status.displayName),
                selected: _filter.status == status,
                onSelected: (selected) {
                  setState(() {
                    _filter = _filter.copyWith(
                      status: selected ? status : null,
                    );
                  });
                },
              );
            }).toList(),
          ),
          
          const SizedBox(height: 16),
          
          // Price Range
          Text(
            'نطاق السعر (ر.ي)',
            style: Theme.of(context).textTheme.titleMedium,
          ),
          const SizedBox(height: 8),
          Row(
            children: [
              Expanded(
                child: TextFormField(
                  initialValue: _filter.minPrice?.toString() ?? '',
                  decoration: const InputDecoration(
                    labelText: 'الحد الأدنى',
                    border: OutlineInputBorder(),
                  ),
                  keyboardType: TextInputType.number,
                  onChanged: (value) {
                    final price = double.tryParse(value);
                    setState(() {
                      _filter = _filter.copyWith(minPrice: price);
                    });
                  },
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: TextFormField(
                  initialValue: _filter.maxPrice?.toString() ?? '',
                  decoration: const InputDecoration(
                    labelText: 'الحد الأعلى',
                    border: OutlineInputBorder(),
                  ),
                  keyboardType: TextInputType.number,
                  onChanged: (value) {
                    final price = double.tryParse(value);
                    setState(() {
                      _filter = _filter.copyWith(maxPrice: price);
                    });
                  },
                ),
              ),
            ],
          ),
          
          const SizedBox(height: 16),
          
          // Bedrooms and Bathrooms
          Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'عدد الغرف',
                      style: Theme.of(context).textTheme.titleMedium,
                    ),
                    const SizedBox(height: 8),
                    DropdownButtonFormField<int>(
                      value: _filter.bedrooms,
                      decoration: const InputDecoration(
                        border: OutlineInputBorder(),
                      ),
                      items: [
                        const DropdownMenuItem(value: null, child: Text('أي عدد')),
                        ...List.generate(6, (index) => index + 1).map(
                          (count) => DropdownMenuItem(
                            value: count,
                            child: Text('$count+'),
                          ),
                        ),
                      ],
                      onChanged: (value) {
                        setState(() {
                          _filter = _filter.copyWith(bedrooms: value);
                        });
                      },
                    ),
                  ],
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'عدد الحمامات',
                      style: Theme.of(context).textTheme.titleMedium,
                    ),
                    const SizedBox(height: 8),
                    DropdownButtonFormField<int>(
                      value: _filter.bathrooms,
                      decoration: const InputDecoration(
                        border: OutlineInputBorder(),
                      ),
                      items: [
                        const DropdownMenuItem(value: null, child: Text('أي عدد')),
                        ...List.generate(4, (index) => index + 1).map(
                          (count) => DropdownMenuItem(
                            value: count,
                            child: Text('$count+'),
                          ),
                        ),
                      ],
                      onChanged: (value) {
                        setState(() {
                          _filter = _filter.copyWith(bathrooms: value);
                        });
                      },
                    ),
                  ],
                ),
              ),
            ],
          ),
          
          const SizedBox(height: 16),
          
          // Features
          Text(
            'المميزات',
            style: Theme.of(context).textTheme.titleMedium,
          ),
          const SizedBox(height: 8),
          Wrap(
            spacing: 8,
            runSpacing: 8,
            children: [
              FilterChip(
                label: const Text('موقف سيارة'),
                selected: _filter.hasParking == true,
                onSelected: (selected) {
                  setState(() {
                    _filter = _filter.copyWith(hasParking: selected ? true : null);
                  });
                },
              ),
              FilterChip(
                label: const Text('مصعد'),
                selected: _filter.hasElevator == true,
                onSelected: (selected) {
                  setState(() {
                    _filter = _filter.copyWith(hasElevator: selected ? true : null);
                  });
                },
              ),
              FilterChip(
                label: const Text('حديقة'),
                selected: _filter.hasGarden == true,
                onSelected: (selected) {
                  setState(() {
                    _filter = _filter.copyWith(hasGarden: selected ? true : null);
                  });
                },
              ),
              FilterChip(
                label: const Text('مسبح'),
                selected: _filter.hasPool == true,
                onSelected: (selected) {
                  setState(() {
                    _filter = _filter.copyWith(hasPool: selected ? true : null);
                  });
                },
              ),
              FilterChip(
                label: const Text('موثق'),
                selected: _filter.isVerified == true,
                onSelected: (selected) {
                  setState(() {
                    _filter = _filter.copyWith(isVerified: selected ? true : null);
                  });
                },
              ),
            ],
          ),
          
          const SizedBox(height: 24),
          
          // Action Buttons
          Row(
            children: [
              Expanded(
                child: OutlinedButton(
                  onPressed: () {
                    ref.read(propertiesProvider.notifier).clearFilters();
                    Navigator.pop(context);
                  },
                  child: const Text('مسح الفلاتر'),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: ElevatedButton(
                  onPressed: () {
                    ref.read(propertiesProvider.notifier).applyFilter(_filter);
                    Navigator.pop(context);
                  },
                  child: const Text('تطبيق'),
                ),
              ),
            ],
          ),
          
          // Bottom padding for safe area
          SizedBox(height: MediaQuery.of(context).padding.bottom),
        ],
      ),
    );
  }
}
