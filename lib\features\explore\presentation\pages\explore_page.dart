import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';

import '../../../../core/constants/app_constants.dart';
import '../../../../core/constants/app_strings.dart';
import '../../../../core/models/property_model.dart';
import '../../../properties/providers/properties_provider.dart';
import '../widgets/filter_bottom_sheet.dart';
import '../widgets/property_card.dart';

class ExplorePage extends ConsumerStatefulWidget {
  const ExplorePage({super.key});

  @override
  ConsumerState<ExplorePage> createState() => _ExplorePageState();
}

class _ExplorePageState extends ConsumerState<ExplorePage> {
  final _searchController = TextEditingController();

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final propertiesState = ref.watch(propertiesProvider);

    return Scaffold(
      appBar: AppBar(
        title: const Text(AppStrings.appName),
        centerTitle: true,
        actions: [
          IconButton(
            icon: const Icon(Icons.notifications_outlined),
            onPressed: () {
              context.push('/notifications');
            },
          ),
        ],
      ),
      body: SafeArea(
        child: Column(
          children: [
            // Search Bar
            Padding(
              padding: const EdgeInsets.all(AppConstants.defaultPadding),
              child: TextField(
                controller: _searchController,
                onChanged: (value) {
                  ref.read(propertiesProvider.notifier).searchProperties(value);
                },
                decoration: InputDecoration(
                  hintText: 'ابحث عن العقارات...',
                  prefixIcon: const Icon(Icons.search),
                  suffixIcon: IconButton(
                    icon: const Icon(Icons.filter_list),
                    onPressed: () {
                      showModalBottomSheet(
                        context: context,
                        isScrollControlled: true,
                        builder: (context) => const FilterBottomSheet(),
                      );
                    },
                  ),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(AppConstants.defaultRadius),
                  ),
                ),
              ),
            ),

            // Quick Filters
            SizedBox(
              height: 50,
              child: ListView(
                scrollDirection: Axis.horizontal,
                padding: const EdgeInsets.symmetric(horizontal: AppConstants.defaultPadding),
                children: [
                  _buildQuickFilter('الكل', true),
                  _buildQuickFilter('للبيع', false),
                  _buildQuickFilter('للإيجار', false),
                  _buildQuickFilter('شقق', false),
                  _buildQuickFilter('فلل', false),
                  _buildQuickFilter('مكاتب', false),
                ],
              ),
            ),

            const SizedBox(height: 16),

            // Properties List
            Expanded(
              child: propertiesState.isLoading
                  ? const Center(child: CircularProgressIndicator())
                  : propertiesState.filteredProperties.isEmpty
                      ? const Center(
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Icon(
                                Icons.search_off,
                                size: 64,
                                color: Colors.grey,
                              ),
                              SizedBox(height: 16),
                              Text(
                                'لا توجد عقارات',
                                style: TextStyle(
                                  fontSize: 18,
                                  fontWeight: FontWeight.bold,
                                  color: Colors.grey,
                                ),
                              ),
                              SizedBox(height: 8),
                              Text(
                                'جرب تغيير معايير البحث',
                                style: TextStyle(
                                  fontSize: 14,
                                  color: Colors.grey,
                                ),
                              ),
                            ],
                          ),
                        )
                      : RefreshIndicator(
                          onRefresh: () => ref.read(propertiesProvider.notifier).refreshProperties(),
                          child: ListView.builder(
                            padding: const EdgeInsets.symmetric(horizontal: AppConstants.defaultPadding),
                            itemCount: propertiesState.filteredProperties.length,
                            itemBuilder: (context, index) {
                              final property = propertiesState.filteredProperties[index];
                              return PropertyCard(
                                property: property,
                                onTap: () {
                                  context.push('/property/${property.id}');
                                },
                              );
                            },
                          ),
                        ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildQuickFilter(String label, bool isSelected) {
    return Container(
      margin: const EdgeInsets.only(right: 8),
      child: FilterChip(
        label: Text(label),
        selected: isSelected,
        onSelected: (selected) {
          // Apply quick filter based on label
          if (selected) {
            _applyQuickFilter(label);
          } else {
            ref.read(propertiesProvider.notifier).clearFilters();
          }
        },
        selectedColor: Theme.of(context).primaryColor.withOpacity(0.2),
        checkmarkColor: Theme.of(context).primaryColor,
      ),
    );
  }

  void _applyQuickFilter(String label) {
    final notifier = ref.read(propertiesProvider.notifier);

    switch (label) {
      case 'شقق':
        notifier.applyFilter(const PropertyFilter(type: PropertyType.apartment));
        break;
      case 'فلل':
        notifier.applyFilter(const PropertyFilter(type: PropertyType.villa));
        break;
      case 'للبيع':
        notifier.applyFilter(const PropertyFilter(status: PropertyStatus.forSale));
        break;
      case 'للإيجار':
        notifier.applyFilter(const PropertyFilter(status: PropertyStatus.forRent));
        break;
      default:
        // For other filters, open the full filter sheet
        showModalBottomSheet(
          context: context,
          isScrollControlled: true,
          builder: (context) => const FilterBottomSheet(),
        );
    }
  }

}
